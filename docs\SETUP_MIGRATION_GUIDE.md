# Setup Command Migration and Enhancement Guide

## Overview

This guide documents the migration from file-based server configuration to MongoDB and the implementation of the enhanced interactive setup command system.

## What's New

### 🗄️ MongoDB Integration
- **Database**: MongoDB Atlas with connection string provided
- **Models**: Mongoose schemas for server configurations and shared data
- **Fallback**: Automatic fallback to file-based config if database is unavailable
- **Migration**: Automated migration script to transfer existing data

### 🎮 Enhanced Setup Command
- **Interactive Interface**: Multi-step button-based setup experience
- **Session Management**: Prevents concurrent setups and handles timeouts
- **Permission Checks**: Requires MANAGE_GUILD permission
- **Auto-Creation**: Can automatically create channels and roles
- **Real-time Updates**: Immediate configuration saving to database

## File Structure

```
├── config/
│   ├── database.js              # MongoDB connection configuration
│   └── serverConfigs.js         # Legacy file-based config (backup)
├── models/
│   ├── ServerConfig.js          # MongoDB schema for server configurations
│   └── SharedConfig.js          # MongoDB schema for shared data
├── services/
│   ├── configService.js         # Database service layer
│   └── configWrapper.js         # Compatibility wrapper
├── commands/slashCommands/info/
│   └── setup-arise-crossover.js # Enhanced interactive setup command
├── utils/
│   └── migrationScript.js       # Data migration utility
└── test/
    ├── test-database-migration.js
    └── test-enhanced-setup.js
```

## Database Schema

### ServerConfig Collection
```javascript
{
  serverId: String,           // Discord server ID
  name: String,              // Server name
  dungeonAlert: {
    enabled: Boolean,
    targetChannelId: String,
    dungeonRoles: {
      E, D, C, B, A, S, SS: String,
      DUNGEON_PING: String,
      RED_DUNGEON: String,
      DOUBLE_DUNGEON: String
    },
    worldRoles: {
      1: String,             // World 1 ping role
      2: String              // World 2 ping role
    },
    islandRoles: {
      "Island Name": String  // Island-specific roles
    }
  },
  worldBossAlert: { ... },
  infernalAlert: { ... },
  createdAt: Date,
  updatedAt: Date
}
```

### SharedConfig Collection
```javascript
{
  configType: "shared",
  worldIslands: {
    1: [Array of World 1 islands],
    2: [Array of World 2 islands]
  },
  islandBosses: {
    "Island Name": "Boss Name"
  },
  rankColors: { ... },
  worldBossColors: { ... }
}
```

## Migration Process

### 1. Automatic Migration
The migration runs automatically when the bot starts if MongoDB is available:

```bash
# Run migration manually
node utils/migrationScript.js

# Test migration
node test/test-database-migration.js
```

### 2. Backup Creation
- Creates `config/serverConfigs.backup.js` before migration
- Original file remains untouched for rollback if needed

### 3. Data Transfer
- Migrates all server configurations to MongoDB
- Preserves all existing settings and role mappings
- Creates shared configuration document

## Enhanced Setup Command

### Usage
```
/setup arise_crossover
/setup arise_crossover feature:auto_dungeons
```

### Features

#### 🎯 Interactive Flow
1. **Feature Selection**: Choose between Auto Dungeons, World Boss (coming soon)
2. **Channel Setup**: Select existing channel or create new one
3. **Role Configuration**: Use existing roles or auto-create with proper colors
4. **Final Confirmation**: Review and save configuration

#### 🔧 Auto-Creation Capabilities
- **Channels**: Creates `#dungeons` channel with proper permissions
- **Roles**: Creates all rank roles (E-SS), ping roles, world roles with correct colors
- **Permissions**: Checks bot permissions before attempting creation

#### 🛡️ Safety Features
- **Session Management**: Prevents multiple concurrent setups
- **Timeout Handling**: 10-minute session timeout
- **Permission Validation**: Requires MANAGE_GUILD permission
- **Error Recovery**: Graceful error handling with user feedback

### Button Interactions

#### Main Flow
- `🌀 Auto Dungeons` - Configure dungeon alerts
- `🌍 Auto World Boss` - Coming soon
- `❌ Cancel` - Cancel setup

#### Channel Selection
- `🏷️ Use Existing Channel` - Select from dropdown
- `🆕 Create New Channel` - Auto-create #dungeons
- `🔙 Back` - Return to previous step

#### Role Setup
- `🎭 Use Existing Roles` - Select from server roles
- `🆕 Create New Roles` - Auto-create all necessary roles
- `✅ Finish Setup` - Complete configuration

## Configuration Service

### ConfigWrapper
Provides backward compatibility and automatic fallback:

```javascript
const configWrapper = require('./services/configWrapper');

// Automatically uses database or falls back to file
const servers = await configWrapper.getEnabledDungeonServers();
const config = await configWrapper.getServerConfig(serverId);
```

### Direct Database Access
For new features that require database-only functionality:

```javascript
const configService = require('./services/configService');

// Save new configuration
await configService.saveServerConfig(serverId, configData);

// Get cached configuration
const config = await configService.getServerConfig(serverId);
```

## Testing

### Run All Tests
```bash
# Test database connection and migration
node test/test-database-migration.js

# Test enhanced setup functionality
node test/test-enhanced-setup.js

# Test legacy setup command
node test/test-setup-command.js
```

### Test Results
All tests should show ✅ status:
- Database connection
- Configuration CRUD operations
- Migration functionality
- Fallback mechanisms
- Setup command files

## Deployment Checklist

### ✅ Pre-Deployment
- [ ] MongoDB connection string configured
- [ ] Mongoose dependency installed (`npm install mongoose`)
- [ ] Migration script tested
- [ ] Backup of existing configuration created

### ✅ Deployment
- [ ] Deploy new code
- [ ] Bot automatically connects to database
- [ ] Migration runs on first startup
- [ ] Verify setup command works: `/setup arise_crossover`

### ✅ Post-Deployment
- [ ] Test setup command in development server
- [ ] Verify existing configurations still work
- [ ] Monitor for any database connection issues
- [ ] Update documentation for server administrators

## Troubleshooting

### Database Connection Issues
```javascript
// Check connection status
const databaseConnection = require('./config/database');
const status = databaseConnection.getConnectionStatus();
console.log(status);
```

### Fallback to File Config
If database is unavailable, the system automatically falls back to file-based configuration:
```
⚠️ Database config failed, falling back to file
```

### Migration Issues
- Check `config/serverConfigs.backup.js` for original data
- Re-run migration: `node utils/migrationScript.js`
- Verify MongoDB permissions and connection string

## Support

### Commands for Administrators
- `/setup arise_crossover` - Interactive setup
- Check logs for database connection status
- Use test scripts to verify functionality

### For Developers
- Review `services/configWrapper.js` for compatibility layer
- Check `models/` for database schemas
- Use `test/` scripts for debugging

## Future Enhancements

### Planned Features
- World Boss setup integration
- Infernal Castle configuration
- Bulk server management
- Configuration export/import
- Advanced role mapping options

### Migration Path
The system is designed to be fully backward compatible while providing enhanced functionality through the database integration.
