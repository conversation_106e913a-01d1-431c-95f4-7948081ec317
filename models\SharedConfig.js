const mongoose = require('mongoose');

// Schema for world islands mapping
const WorldIslandsSchema = new mongoose.Schema({
    1: [{ type: String }], // World 1 islands
    2: [{ type: String }]  // World 2 islands
}, { _id: false });

// Schema for island bosses mapping
const IslandBossesSchema = new mongoose.Schema({
    'Leveling City': { type: String, default: 'Vermillion' },
    'Grass Village': { type: String, default: 'Dor' },
    'Brum Island': { type: String, default: 'Mifalcon' },
    'Faceheal Town': { type: String, default: 'Murcielago' },
    'Lucky Kingdom': { type: String, default: 'Time King' },
    'Nipon City': { type: String, default: 'Chainsaw' },
    'Mori Town': { type: String, default: 'Gucci' },
    'Dragon City': { type: String, default: 'Frioo' },
    'XZ City': { type: String, default: 'Paitama' },
    'Kindama City': { type: String, default: 'Tuturum' },
    'Hunters City': { type: String, default: 'Dae In' },
    'Nen City': { type: String, default: 'God Speed' }
}, { _id: false });

// Schema for rank colors
const RankColorsSchema = new mongoose.Schema({
    E: { type: String, default: '#8897aa' },
    D: { type: String, default: '#4488ff' },
    C: { type: String, default: '#44aaff' },
    B: { type: String, default: '#6644ff' },
    A: { type: String, default: '#8844ff' },
    S: { type: String, default: '#aa44ff' },
    SS: { type: String, default: '#ff44ff' }
}, { _id: false });

// Schema for world boss colors
const WorldBossColorsSchema = new mongoose.Schema({
    'Boto': { type: String, default: '#8897aa' },    // E rank color
    'Baruto': { type: String, default: '#4488ff' },  // D rank color
    'Baizen': { type: String, default: '#44aaff' },  // C rank color
    'Force': { type: String, default: '#6644ff' },   // B rank color
    'Begeta': { type: String, default: '#8844ff' },  // A rank color
    'Alien': { type: String, default: '#aa44ff' },   // S rank color
    'Gon': { type: String, default: '#ff44ff' }      // SS rank color
}, { _id: false });

// Schema for world boss mapping
const WorldBossMappingSchema = new mongoose.Schema({
    'Leveling City': { type: String, default: 'Boto' },
    'Grass Village': { type: String, default: 'Baruto' },
    'Faceheal Town': { type: String, default: 'Baizen' },
    'Nipon City': { type: String, default: 'Force' },
    'Dragon City': { type: String, default: 'Begeta' },
    'Kindama City': { type: String, default: 'Alien' },
    'Nen City': { type: String, default: 'Gon' }
}, { _id: false });

// Main shared configuration schema
const SharedConfigSchema = new mongoose.Schema({
    configType: { 
        type: String, 
        default: 'shared',
        unique: true,
        index: true 
    },
    worldIslands: { 
        type: WorldIslandsSchema, 
        default: () => ({
            1: ['Leveling City', 'Grass Village', 'Brum Island', 'Faceheal Town', 'Lucky Kingdom', 'Nipon City', 'Mori Town'],
            2: ['Dragon City', 'XZ City', 'Kindama City', 'Hunters City', 'Nen City']
        })
    },
    islandBosses: { 
        type: IslandBossesSchema, 
        default: () => ({}) 
    },
    rankColors: { 
        type: RankColorsSchema, 
        default: () => ({}) 
    },
    worldBossColors: { 
        type: WorldBossColorsSchema, 
        default: () => ({}) 
    },
    worldBossMapping: { 
        type: WorldBossMappingSchema, 
        default: () => ({}) 
    },
    updatedAt: { 
        type: Date, 
        default: Date.now 
    }
}, {
    timestamps: true,
    collection: 'sharedconfigs'
});

// Update the updatedAt field before saving
SharedConfigSchema.pre('save', function(next) {
    this.updatedAt = new Date();
    next();
});

// Static methods
SharedConfigSchema.statics.getSharedConfig = async function() {
    let config = await this.findOne({ configType: 'shared' });
    
    if (!config) {
        // Create default shared config if it doesn't exist
        config = new this({
            configType: 'shared',
            worldIslands: {
                1: ['Leveling City', 'Grass Village', 'Brum Island', 'Faceheal Town', 'Lucky Kingdom', 'Nipon City', 'Mori Town'],
                2: ['Dragon City', 'XZ City', 'Kindama City', 'Hunters City', 'Nen City']
            },
            islandBosses: {
                'Leveling City': 'Vermillion',
                'Grass Village': 'Dor',
                'Brum Island': 'Mifalcon',
                'Faceheal Town': 'Murcielago',
                'Lucky Kingdom': 'Time King',
                'Nipon City': 'Chainsaw',
                'Mori Town': 'Gucci',
                'Dragon City': 'Frioo',
                'XZ City': 'Paitama',
                'Kindama City': 'Tuturum',
                'Hunters City': 'Dae In',
                'Nen City': 'God Speed'
            },
            rankColors: {
                E: '#8897aa',
                D: '#4488ff',
                C: '#44aaff',
                B: '#6644ff',
                A: '#8844ff',
                S: '#aa44ff',
                SS: '#ff44ff'
            },
            worldBossColors: {
                'Boto': '#8897aa',
                'Baruto': '#4488ff',
                'Baizen': '#44aaff',
                'Force': '#6644ff',
                'Begeta': '#8844ff',
                'Alien': '#aa44ff',
                'Gon': '#ff44ff'
            },
            worldBossMapping: {
                'Leveling City': 'Boto',
                'Grass Village': 'Baruto',
                'Faceheal Town': 'Baizen',
                'Nipon City': 'Force',
                'Dragon City': 'Begeta',
                'Kindama City': 'Alien',
                'Nen City': 'Gon'
            }
        });
        
        await config.save();
    }
    
    return config;
};

const SharedConfig = mongoose.model('SharedConfig', SharedConfigSchema);

module.exports = SharedConfig;
