// Multi-server configuration for dungeon alerts and world boss alerts
// This allows the bot to work across multiple Discord servers with server-specific settings

module.exports = {
    // Default server configuration (current main server)
    '1362356687092191442': {
        name: 'RankBreaker Main Server',
        dungeonAlert: {
            enabled: true,
            targetChannelId: '1368266784532205650',
            dungeonRoles: {
                E: '1364560442910969866',
                D: '1364560380709306389',
                C: '1364560082519588875',
                B: '1364560075770826782',
                A: '1364560068133126144',
                S: '1364560060923252766',
                SS: '1364559909722652723',
                DUNGEON_PING: '1364559489604390996',
                RED_DUNGEON: '1364559668772605992',
                DOUBLE_DUNGEON: '1364559627144003645'
            },
            worldRoles: {
                1: '1380529138309529762', // World 1 ping role
                2: '1380529145938972712'  // World 2 ping role
            },
            islandRoles: {
                'Leveling City': '1371844586003103865',
                'Grass Village': '1371844603040370759',
                'Brum Island': '1371844612196532304',
                'Faceheal Town': '1371844622015402086',
                'Lucky Kingdom': '1371844631092003037',
                'Nipon City': '1371844639430021132',
                'Mori Town': '1371844649798602833',
                'Dragon City': '1371844984244015256',
                'XZ City': '1371845004598968441',
                'Kindama City': '1371845011116785674',
                'Hunters City': '1373302432389398559',
                'Nen City': '1380528415031033886'
            }
        },
        worldBossAlert: {
            enabled: true,
            targetChannelId: '1381910717573365771',
            roleId: '1381911057316057158',
            worldBossRoles: {
                'Leveling City': '1382042037716783204',  // Boto
                'Grass Village': '1382042081874411590',  // Baruto
                'Faceheal Town': '1382042094767575230',  // Baizen
                'Nipon City': '1382260491925196841',     // Force
                'Dragon City': '1382041920666206278',    // Begeta
                'Kindama City': '1382260587127373904',   // Alien
                'Nen City': '1382041840236106002'        // Gon
            }
        },
        infernalAlert: {
            enabled: true,
            targetChannelId: '1372921982483435581',
            generalAlertRole: '1372921375005737003',
            monarchAlertRole: '1373306924115824740',
            webhookName: 'RankBreaker',
            webhookAvatar: 'https://cdn.discordapp.com/icons/1362356687092191442/25b53ae035c74c9d6edcf8ca11dfc205.webp?size=1024',
            messageFormat: 'default' // 'default' or 'custom'
        }
    },
    // Example configuration for additional servers
    // Uncomment and modify as needed for more servers
    /*
    'ANOTHER_SERVER_ID_HERE': {
        name: 'Second Server Name',
        dungeonAlert: {
            enabled: true,
            targetChannelId: 'DUNGEON_CHANNEL_ID_HERE',
            dungeonRoles: {
                E: 'E_RANK_ROLE_ID',
                D: 'D_RANK_ROLE_ID',
                C: 'C_RANK_ROLE_ID',
                B: 'B_RANK_ROLE_ID',
                A: 'A_RANK_ROLE_ID',
                S: 'S_RANK_ROLE_ID',
                SS: 'SS_RANK_ROLE_ID',
                DUNGEON_PING: 'GENERAL_DUNGEON_PING_ROLE_ID',
                RED_DUNGEON: 'RED_DUNGEON_ROLE_ID',
                DOUBLE_DUNGEON: 'DOUBLE_DUNGEON_ROLE_ID'
            },
            worldRoles: {
                1: 'WORLD_1_ROLE_ID',
                2: 'WORLD_2_ROLE_ID'
            },
            islandRoles: {
                'Leveling City': 'LEVELING_CITY_ROLE_ID',
                'Grass Village': 'GRASS_VILLAGE_ROLE_ID',
                'Brum Island': 'BRUM_ISLAND_ROLE_ID',
                'Faceheal Town': 'FACEHEAL_TOWN_ROLE_ID',
                'Lucky Kingdom': 'LUCKY_KINGDOM_ROLE_ID',
                'Nipon City': 'NIPON_CITY_ROLE_ID',
                'Mori Town': 'MORI_TOWN_ROLE_ID',
                'Dragon City': 'DRAGON_CITY_ROLE_ID',
                'XZ City': 'XZ_CITY_ROLE_ID',
                'Kindama City': 'KINDAMA_CITY_ROLE_ID',
                'Hunters City': 'HUNTERS_CITY_ROLE_ID',
                'Nen City': 'NEN_CITY_ROLE_ID'
            }
        },
        worldBossAlert: {
            enabled: true,
            targetChannelId: 'WORLD_BOSS_CHANNEL_ID_HERE',
            roleId: 'WORLD_BOSS_ROLE_ID_HERE',
            worldBossRoles: {
                'Leveling City': 'BOTO_ROLE_ID',
                'Grass Village': 'BARUTO_ROLE_ID',
                'Faceheal Town': 'BAIZEN_ROLE_ID',
                'Nipon City': 'FORCE_ROLE_ID',
                'Dragon City': 'BEGETA_ROLE_ID',
                'Kindama City': 'ALIEN_ROLE_ID',
                'Nen City': 'GON_ROLE_ID'
            }
        },
        infernalAlert: {
            enabled: true,
            targetChannelId: 'INFERNAL_CHANNEL_ID_HERE',
            generalAlertRole: 'INFERNAL_GENERAL_ROLE_ID',
            monarchAlertRole: 'INFERNAL_MONARCH_ROLE_ID',
            webhookName: 'BotName',
            webhookAvatar: 'WEBHOOK_AVATAR_URL_HERE',
            messageFormat: 'default' // 'default' or 'custom'
        }
    },
    */

    // Hellspire (Auto-generated)
    '1374046574270746777': {
        name: 'Hellspire',
        dungeonAlert: {
            enabled: true,
            targetChannelId: '1377799826195877918',
            dungeonRoles: {
                E: '1382917471316742218',
                D: '1382917475091480687',
                C: '1382917477956194320',
                B: '1382917482310012940',
                A: '1382917487259156541',
                S: '1382917491113857116',
                SS: '1382917494720827434',
                DUNGEON_PING: '1382917498105626636',
                RED_DUNGEON: '1382917502119448617',
                DOUBLE_DUNGEON: '1382917506670526464'
            },
            worldRoles: {
                1: '1382917510348800122',
                2: '1382917513041678407'
            },
            islandRoles: {
                'Leveling City': '1382917516195663883',
                'Grass Village': '1382917520440430663',
                'Brum Island': '1382917524559106048',
                'Faceheal Town': '1382917528086384760',
                'Lucky Kingdom': '1382917537548865638',
                'Nipon City': '1382917543907295284',
                'Mori Town': '1382917548210917436',
                'Dragon City': '1382917551519957133',
                'XZ City': '1382917554913148929',
                'Kindama City': '1382917558331637762',
                'Hunters City': '1382917562240864326',
                'Nen City': '1382917566003154975'
            }
        }
    },

    // Six Shadows GUILD Configuration (Auto-generated)
    '1353186342682497124': {
        name: 'Six Shadows GUILD',
        dungeonAlert: {
            enabled: true,
            targetChannelId: '1379160904360267816',
            dungeonRoles: {
                E: '1382927848259452939',
                D: '1382927852160290857',
                C: '1382927856664842250',
                B: '1382927861014597684',
                A: '1382927865150181386',
                S: '1382927869310931044',
                SS: '1382927873882591373',
                DUNGEON_PING: '1382927878307713085',
                RED_DUNGEON: '1382927883697131521',
                DOUBLE_DUNGEON: '1382927888394883183'
            },
            worldRoles: {
                1: '1382927897681203243',
                2: '1382927901896212610'
            },
            islandRoles: {
                'Leveling City': '1382927905746718791',
                'Grass Village': '1382927909987160156',
                'Brum Island': '1382927914156294285',
                'Faceheal Town': '1382927918253998111',
                'Lucky Kingdom': '1382927922603626566',
                'Nipon City': '1382927926391078982',
                'Mori Town': '1382927931315191818',
                'Dragon City': '1382927935677272084',
                'XZ City': '1382927939120922679',
                'Kindama City': '1382927943256244274',
                'Hunters City': '1382927946993504286',
                'Nen City': '1382927950567047241'
            }
        }
    },

    // ApsoluteOne Configuration (Auto-generated)
    '1371212189448278087': {
        name: 'ApsoluteOne',
        dungeonAlert: {
            enabled: true,
            targetChannelId: '1382586393100025957',
            dungeonRoles: {
                E: '1383011760231026729',
                D: '1383011764014153809',
                C: '1383011768606916638',
                B: '1383011773971693588',
                A: '1383011778270855258',
                S: '1383011784633618445',
                SS: '1383011789557727285',
                DUNGEON_PING: '1383011794141974558',
                RED_DUNGEON: '1383011797451276291',
                DOUBLE_DUNGEON: '1383011802085986387'
            },
            worldRoles: {
                1: '1383011806125101126',
                2: '1383011816086569062'
            },
            islandRoles: {
                'Leveling City': '1383011820754964590',
                'Grass Village': '1383011825137881139',
                'Brum Island': '1383011829264941076',
                'Faceheal Town': '1383011833019109396',
                'Lucky Kingdom': '1383011836223422518',
                'Nipon City': '1383011841025773568',
                'Mori Town': '1383011846101012500',
                'Dragon City': '1383011850022551554',
                'XZ City': '1383011854804320379',
                'Kindama City': '1383011859115933716',
                'Hunters City': '1383011862647672877',
                'Nen City': '1383011866737115157'
            }
        }
    },

    // Revengers Guild v2 Configuration (Auto-generated)
    '1377292323456549026': {
        name: 'Revengers Guild v2',
        dungeonAlert: {
            enabled: true,
            targetChannelId: '1383015654130450482',
            dungeonRoles: {
                E: '1383015918212485170',
                D: '1383015922922684436',
                C: '1383015933853040660',
                B: '1383015937908932638',
                A: '1383015941528617032',
                S: '1383015950349238332',
                SS: '1383015954216128593',
                DUNGEON_PING: '1383015958167294033',
                RED_DUNGEON: '1383015961677795490',
                DOUBLE_DUNGEON: '1383015966023221312'
            },
            worldRoles: {
                1: '1383015969320075316',
                2: '1383015973103079504'
            },
            islandRoles: {
                'Leveling City': '1383015977041662062',
                'Grass Village': '1383015980334190624',
                'Brum Island': '1383015984939532288',
                'Faceheal Town': '1383015989066858537',
                'Lucky Kingdom': '1383015992887611437',
                'Nipon City': '1383015997170126948',
                'Mori Town': '1383016000999522364',
                'Dragon City': '1383016004728127510',
                'XZ City': '1383016008112930839',
                'Kindama City': '1383016012571738212',
                'Hunters City': '1383016015985770527',
                'Nen City': '1383016019743735819'
            }
        }
    },

    // INF REIGN Configuration (Auto-generated)
    '1370343058733928578': {
        name: 'INF REIGN',
        dungeonAlert: {
            enabled: true,
            targetChannelId: '1382977601940750407',
            dungeonRoles: {
                E: '1383025711153156166',
                D: '1383025715137482782',
                C: '1383025719189307465',
                B: '1383025722708328621',
                A: '1383025726558699631',
                S: '1383025730916716595',
                SS: '1383025734792122510',
                DUNGEON_PING: '1383025738478784566',
                RED_DUNGEON: '1383025742253916262',
                DOUBLE_DUNGEON: '1383025746016206932'
            },
            worldRoles: {
                1: '1383025749904195624',
                2: '1383025753582469130'
            },
            islandRoles: {
                'Leveling City': '1383025757508472842',
                'Grass Village': '1383025761447051384',
                'Brum Island': '1383025765448155307',
                'Faceheal Town': '1383025769898446848',
                'Lucky Kingdom': '1383025773614727231',
                'Nipon City': '1383025777884401745',
                'Mori Town': '1383025782007529615',
                'Dragon City': '1383025785790533745',
                'XZ City': '1383025789791899710',
                'Kindama City': '1383025793675821157',
                'Hunters City': '1383025797090250845',
                'Nen City': '1383025800684769340'
            }
        }
    },

    // Aqua Deer Configuration (Auto-generated)
    '1150767996625768499': {
        name: 'Aqua Deer',
        dungeonAlert: {
            enabled: true,
            targetChannelId: '1377464239496892426',
            dungeonRoles: {
                E: '1383054423244148917',
                D: '1383054426935394354',
                C: '1383054431385288724',
                B: '1383054435592179754',
                A: '1383054439350276197',
                S: '1383054443808952502',
                SS: '1383054447940210798',
                DUNGEON_PING: '1383054452302282773',
                RED_DUNGEON: '1383054456043733037',
                DOUBLE_DUNGEON: '1383054460586299422'
            },
            worldRoles: {
                1: '1383054464931336212',
                2: '1383054468798484512'
            },
            islandRoles: {
                'Leveling City': '1383054472691056793',
                'Grass Village': '1383054477145411656',
                'Brum Island': '1383054480911896576',
                'Faceheal Town': '1383054484464341076',
                'Lucky Kingdom': '1383054488713035789',
                'Nipon City': '1383054493859446835',
                'Mori Town': '1383054498267660309',
                'Dragon City': '1383054502512295986',
                'XZ City': '1383054506660462695',
                'Kindama City': '1383054510318026764',
                'Hunters City': '1383054514998739068',
                'Nen City': '1383054518962618390'
            }
        }
    },

    // [Arise Crossover] Hellfire Guild Configuration (Auto-generated)
    '1299736472919740478': {
        name: '[Arise Crossover] Hellfire Guild',
        dungeonAlert: {
            enabled: true,
            targetChannelId: '1378810488191975649',
            dungeonRoles: {
                E: '1383066267543404547',
                D: '1383066272585224203',
                C: '1383066276821340271',
                B: '1383066280306806924',
                A: '1383066284157177926',
                S: '1383066287634120725',
                SS: '1383066291606126623',
                DUNGEON_PING: '1383066295997567038',
                RED_DUNGEON: '1383066300208644138',
                DOUBLE_DUNGEON: '1383066304596017182'
            },
            worldRoles: {
                1: '1383066308719018015',
                2: '1383066312443691009'
            },
            islandRoles: {
                'Leveling City': '1383066316222758927',
                'Grass Village': '1383066320479719476',
                'Brum Island': '1383066324678348962',
                'Faceheal Town': '1383066328809607222',
                'Lucky Kingdom': '1383066333112963264',
                'Nipon City': '1383066337357856809',
                'Mori Town': '1383066341040328714',
                'Dragon City': '1383066344857145454',
                'XZ City': '1383066348560842834',
                'Kindama City': '1383066353254142086',
                'Hunters City': '1383066357062438953',
                'Nen City': '1383066361311268917'
            }
        }
    },

    // Ξquinoχ Configuration (Auto-generated)
    '1345907653154574346': {
        name: 'Ξquinoχ',
        dungeonAlert: {
            enabled: true,
            targetChannelId: '1377909871344877619',
            dungeonRoles: {
                E: '1383074724162048182',
                D: '1383074728406683669',
                C: '1383074732202524692',
                B: '1383074736057221191',
                A: '1383074739819647068',
                S: '1383074744047505459',
                SS: '1383074748698984568',
                DUNGEON_PING: '1383074753270648943',
                RED_DUNGEON: '1383074757041192964',
                DOUBLE_DUNGEON: '1383074760984100964'
            },
            worldRoles: {
                1: '1383074766172459098',
                2: '1383074769951264768'
            },
            islandRoles: {
                'Leveling City': '1383074773629669416',
                'Grass Village': '1383074778017169459',
                'Brum Island': '1383074781972140214',
                'Faceheal Town': '1383074786367766648',
                'Lucky Kingdom': '1383074790377652336',
                'Nipon City': '1383074795138056242',
                'Mori Town': '1383074799311523982',
                'Dragon City': '1383074803249971302',
                'XZ City': '1383074807364587635',
                'Kindama City': '1383074812439826514',
                'Hunters City': '1383074816457703434',
                'Nen City': '1383074820589228174'
            }
        }
    },

    // [Arise Crossover] Hellfire Guild Configuration (Auto-generated)
    '1299736472919740478': {
        name: '[Arise Crossover] Hellfire Guild',
        dungeonAlert: {
            enabled: true,
            targetChannelId: '1378810488191975649',
            dungeonRoles: {
                E: '1383066267543404547',
                D: '1383066272585224203',
                C: '1383066276821340271',
                B: '1383066280306806924',
                A: '1383066284157177926',
                S: '1383066287634120725',
                SS: '1383066291606126623',
                DUNGEON_PING: '1383066295997567038',
                RED_DUNGEON: '1383066300208644138',
                DOUBLE_DUNGEON: '1383066304596017182'
            },
            worldRoles: {
                1: '1383066308719018015',
                2: '1383066312443691009'
            },
            islandRoles: {
                'Leveling City': '1383066316222758927',
                'Grass Village': '1383078749859483811',
                'Brum Island': '1383078754758299799',
                'Faceheal Town': '1383078762777808936',
                'Lucky Kingdom': '1383078766443499540',
                'Nipon City': '1383078769920708690',
                'Mori Town': '1383078773749977110',
                'Dragon City': '1383078778258981025',
                'XZ City': '1383078782394699862',
                'Kindama City': '1383078786211516480',
                'Hunters City': '1383078790376456283',
                'Nen City': '1383078795992367135'
            }
        }
    },

    // Shared configuration that applies to all servers
    shared: {
        // World mapping for islands (same across all servers)
        worldIslands: {
            1: ['Leveling City', 'Grass Village', 'Brum Island', 'Faceheal Town', 'Lucky Kingdom', 'Nipon City', 'Mori Town'],
            2: ['Dragon City', 'XZ City', 'Kindama City', 'Hunters City', 'Nen City']
        },

        // Boss names for each island (same across all servers)
        islandBosses: {
            'Leveling City': 'Vermillion',
            'Grass Village': 'Dor',
            'Brum Island': 'Mifalcon',
            'Faceheal Town': 'Murcielago',
            'Lucky Kingdom': 'Time King',
            'Nipon City': 'Chainsaw',
            'Mori Town': 'Gucci',
            'Dragon City': 'Frioo',
            'XZ City': 'Paitama',
            'Kindama City': 'Tuturum',
            'Hunters City': 'Dae In',
            'Nen City': 'God Speed'
        },

        // World boss island-boss mapping (same across all servers)
        worldBossIslandBosses: {
            'Leveling City': 'Boto',
            'Grass Village': 'Baruto',
            'Faceheal Town': 'Baizen',
            'Nipon City': 'Force',
            'Dragon City': 'Begeta',
            'Kindama City': 'Alien',
            'Nen City': 'Gon'
        },

        // Rank colors for dungeon images (same across all servers)
        rankColors: {
            'E': '#8897aa',
            'D': '#4488ff',
            'C': '#44aaff',
            'B': '#6644ff',
            'A': '#8844ff',
            'S': '#aa44ff',
            'SS': '#ff44ff'
        },

        // World boss colors for images (using rank colors from dungeon alerts)
        worldBossColors: {
            'Boto': '#8897aa',    // E rank color
            'Baruto': '#4488ff',  // D rank color
            'Baizen': '#44aaff',  // C rank color
            'Force': '#6644ff',   // B rank color
            'Begeta': '#8844ff',  // A rank color
            'Alien': '#aa44ff',   // S rank color
            'Gon': '#ff44ff'      // SS rank color
        }
    },

    // Helper function to get server configuration
    getServerConfig: function(serverId) {
        return this[serverId] || null;
    },

    // Helper function to get dungeon alert config for a server
    getDungeonConfig: function(serverId) {
        const serverConfig = this.getServerConfig(serverId);
        return serverConfig?.dungeonAlert || null;
    },

    // Helper function to get world boss alert config for a server
    getWorldBossConfig: function(serverId) {
        const serverConfig = this.getServerConfig(serverId);
        return serverConfig?.worldBossAlert || null;
    },

    // Helper function to get all enabled servers for dungeon alerts
    getEnabledDungeonServers: function() {
        const enabledServers = [];
        for (const [serverId, config] of Object.entries(this)) {
            if (typeof config === 'object' && config.dungeonAlert?.enabled) {
                enabledServers.push({
                    serverId,
                    name: config.name,
                    config: config.dungeonAlert
                });
            }
        }
        return enabledServers;
    },

    // Helper function to get all enabled servers for world boss alerts
    getEnabledWorldBossServers: function() {
        const enabledServers = [];
        for (const [serverId, config] of Object.entries(this)) {
            if (typeof config === 'object' && config.worldBossAlert?.enabled) {
                enabledServers.push({
                    serverId,
                    name: config.name,
                    config: config.worldBossAlert
                });
            }
        }
        return enabledServers;
    },

    // Helper function to get infernal alert config for a server
    getInfernalConfig: function(serverId) {
        const serverConfig = this.getServerConfig(serverId);
        return serverConfig?.infernalAlert || null;
    },

    // Helper function to get all enabled servers for infernal alerts
    getEnabledInfernalServers: function() {
        const enabledServers = [];
        for (const [serverId, config] of Object.entries(this)) {
            if (typeof config === 'object' && config.infernalAlert?.enabled) {
                enabledServers.push({
                    serverId,
                    name: config.name,
                    config: config.infernalAlert
                });
            }
        }
        return enabledServers;
    },

    // Helper function to check if a server has any configuration
    hasServerConfig: function(serverId) {
        return this[serverId] && typeof this[serverId] === 'object' && this[serverId].name;
    },

    // Helper function to get all configured server IDs
    getAllConfiguredServers: function() {
        const servers = [];
        for (const [serverId, config] of Object.entries(this)) {
            if (typeof config === 'object' && config.name) {
                servers.push({
                    serverId,
                    name: config.name,
                    hasDungeonAlert: !!config.dungeonAlert?.enabled,
                    hasWorldBossAlert: !!config.worldBossAlert?.enabled,
                    hasInfernalAlert: !!config.infernalAlert?.enabled
                });
            }
        }
        return servers;
    }
};
