const ServerConfig = require('../models/ServerConfig');
const SharedConfig = require('../models/SharedConfig');
const chalk = require('chalk');

class ConfigService {
    constructor() {
        this.cache = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5 minutes cache
        this.sharedConfigCache = null;
        this.sharedConfigCacheTime = null;
    }

    // Helper method to get server configuration
    async getServerConfig(serverId) {
        try {
            const cacheKey = `server_${serverId}`;
            const cached = this.cache.get(cacheKey);
            
            if (cached && (Date.now() - cached.timestamp) < this.cacheTimeout) {
                return cached.data;
            }

            const config = await ServerConfig.findByServerId(serverId);
            
            if (config) {
                this.cache.set(cacheKey, {
                    data: config,
                    timestamp: Date.now()
                });
            }
            
            return config;
        } catch (error) {
            console.error(chalk.red('❌ Error getting server config:'), error);
            return null;
        }
    }

    // Helper method to get dungeon alert config for a server
    async getDungeonConfig(serverId) {
        try {
            const serverConfig = await this.getServerConfig(serverId);
            return serverConfig?.dungeonAlert || null;
        } catch (error) {
            console.error(chalk.red('❌ Error getting dungeon config:'), error);
            return null;
        }
    }

    // Helper method to get world boss alert config for a server
    async getWorldBossConfig(serverId) {
        try {
            const serverConfig = await this.getServerConfig(serverId);
            return serverConfig?.worldBossAlert || null;
        } catch (error) {
            console.error(chalk.red('❌ Error getting world boss config:'), error);
            return null;
        }
    }

    // Helper method to get infernal alert config for a server
    async getInfernalConfig(serverId) {
        try {
            const serverConfig = await this.getServerConfig(serverId);
            return serverConfig?.infernalAlert || null;
        } catch (error) {
            console.error(chalk.red('❌ Error getting infernal config:'), error);
            return null;
        }
    }

    // Helper method to get all enabled servers for dungeon alerts
    async getEnabledDungeonServers() {
        try {
            const servers = await ServerConfig.getEnabledDungeonServers();
            return servers.map(server => ({
                serverId: server.serverId,
                name: server.name,
                config: server.dungeonAlert
            }));
        } catch (error) {
            console.error(chalk.red('❌ Error getting enabled dungeon servers:'), error);
            return [];
        }
    }

    // Helper method to get all enabled servers for world boss alerts
    async getEnabledWorldBossServers() {
        try {
            const servers = await ServerConfig.getEnabledWorldBossServers();
            return servers.map(server => ({
                serverId: server.serverId,
                name: server.name,
                config: server.worldBossAlert
            }));
        } catch (error) {
            console.error(chalk.red('❌ Error getting enabled world boss servers:'), error);
            return [];
        }
    }

    // Helper method to get all enabled servers for infernal alerts
    async getEnabledInfernalServers() {
        try {
            const servers = await ServerConfig.getEnabledInfernalServers();
            return servers.map(server => ({
                serverId: server.serverId,
                name: server.name,
                config: server.infernalAlert
            }));
        } catch (error) {
            console.error(chalk.red('❌ Error getting enabled infernal servers:'), error);
            return [];
        }
    }

    // Helper method to check if a server has any configuration
    async hasServerConfig(serverId) {
        try {
            const config = await this.getServerConfig(serverId);
            return !!config;
        } catch (error) {
            console.error(chalk.red('❌ Error checking server config:'), error);
            return false;
        }
    }

    // Helper method to get all configured server IDs
    async getAllConfiguredServers() {
        try {
            const servers = await ServerConfig.find({}).select('serverId name dungeonAlert worldBossAlert infernalAlert');
            return servers.map(server => ({
                serverId: server.serverId,
                name: server.name,
                hasDungeonAlert: !!server.dungeonAlert?.enabled,
                hasWorldBossAlert: !!server.worldBossAlert?.enabled,
                hasInfernalAlert: !!server.infernalAlert?.enabled
            }));
        } catch (error) {
            console.error(chalk.red('❌ Error getting all configured servers:'), error);
            return [];
        }
    }

    // Method to get shared configuration
    async getSharedConfig() {
        try {
            // Check cache first
            if (this.sharedConfigCache && 
                this.sharedConfigCacheTime && 
                (Date.now() - this.sharedConfigCacheTime) < this.cacheTimeout) {
                return this.sharedConfigCache;
            }

            const sharedConfig = await SharedConfig.getSharedConfig();
            
            // Cache the result
            this.sharedConfigCache = sharedConfig;
            this.sharedConfigCacheTime = Date.now();
            
            return sharedConfig;
        } catch (error) {
            console.error(chalk.red('❌ Error getting shared config:'), error);
            return null;
        }
    }

    // Method to create or update server configuration
    async saveServerConfig(serverId, configData) {
        try {
            let serverConfig = await ServerConfig.findByServerId(serverId);
            
            if (serverConfig) {
                // Update existing configuration
                Object.assign(serverConfig, configData);
            } else {
                // Create new configuration
                serverConfig = new ServerConfig({
                    serverId,
                    ...configData
                });
            }
            
            await serverConfig.save();
            
            // Clear cache for this server
            this.cache.delete(`server_${serverId}`);
            
            console.log(chalk.green(`✅ Saved server configuration for ${configData.name} (${serverId})`));
            return serverConfig;
        } catch (error) {
            console.error(chalk.red('❌ Error saving server config:'), error);
            throw error;
        }
    }

    // Method to delete server configuration
    async deleteServerConfig(serverId) {
        try {
            const result = await ServerConfig.deleteOne({ serverId });
            
            // Clear cache for this server
            this.cache.delete(`server_${serverId}`);
            
            console.log(chalk.green(`✅ Deleted server configuration for ${serverId}`));
            return result;
        } catch (error) {
            console.error(chalk.red('❌ Error deleting server config:'), error);
            throw error;
        }
    }

    // Method to clear cache
    clearCache() {
        this.cache.clear();
        this.sharedConfigCache = null;
        this.sharedConfigCacheTime = null;
        console.log(chalk.blue('🧹 Configuration cache cleared'));
    }

    // Method to get cache statistics
    getCacheStats() {
        return {
            serverConfigs: this.cache.size,
            sharedConfigCached: !!this.sharedConfigCache,
            cacheTimeout: this.cacheTimeout
        };
    }
}

// Create singleton instance
const configService = new ConfigService();

module.exports = configService;
