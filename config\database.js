const mongoose = require('mongoose');
const chalk = require('chalk');

class DatabaseConnection {
    constructor() {
        this.isConnected = false;
        this.connectionString = 'mongodb+srv://Captain:<EMAIL>/rankbreaker?retryWrites=true&w=majority';
        this.connection = null;
    }

    async connect() {
        try {
            if (this.isConnected) {
                console.log(chalk.yellow('📊 Database already connected'));
                return this.connection;
            }

            console.log(chalk.blue('📊 Connecting to MongoDB...'));
            
            // Configure mongoose options
            const options = {
                maxPoolSize: 10, // Maintain up to 10 socket connections
                serverSelectionTimeoutMS: 5000, // Keep trying to send operations for 5 seconds
                socketTimeoutMS: 45000, // Close sockets after 45 seconds
                bufferCommands: false // Disable mongoose buffering
            };

            this.connection = await mongoose.connect(this.connectionString, options);
            this.isConnected = true;

            console.log(chalk.green('✅ Successfully connected to MongoDB'));
            console.log(chalk.blue(`📊 Database: ${this.connection.connection.name}`));
            console.log(chalk.blue(`📊 Host: ${this.connection.connection.host}`));

            // Handle connection events
            mongoose.connection.on('error', (error) => {
                console.error(chalk.red('❌ MongoDB connection error:'), error);
                this.isConnected = false;
            });

            mongoose.connection.on('disconnected', () => {
                console.log(chalk.yellow('⚠️ MongoDB disconnected'));
                this.isConnected = false;
            });

            mongoose.connection.on('reconnected', () => {
                console.log(chalk.green('✅ MongoDB reconnected'));
                this.isConnected = true;
            });

            return this.connection;

        } catch (error) {
            console.error(chalk.red('❌ Failed to connect to MongoDB:'), error);
            this.isConnected = false;
            throw error;
        }
    }

    async disconnect() {
        try {
            if (!this.isConnected) {
                console.log(chalk.yellow('📊 Database already disconnected'));
                return;
            }

            console.log(chalk.blue('📊 Disconnecting from MongoDB...'));
            await mongoose.disconnect();
            this.isConnected = false;
            this.connection = null;
            console.log(chalk.green('✅ Successfully disconnected from MongoDB'));

        } catch (error) {
            console.error(chalk.red('❌ Error disconnecting from MongoDB:'), error);
            throw error;
        }
    }

    getConnectionStatus() {
        return {
            isConnected: this.isConnected,
            readyState: mongoose.connection.readyState,
            host: mongoose.connection.host,
            name: mongoose.connection.name
        };
    }

    async testConnection() {
        try {
            if (!this.isConnected) {
                await this.connect();
            }

            // Test the connection by running a simple operation
            const admin = mongoose.connection.db.admin();
            const result = await admin.ping();
            
            console.log(chalk.green('✅ Database connection test successful'));
            return { success: true, result };

        } catch (error) {
            console.error(chalk.red('❌ Database connection test failed:'), error);
            return { success: false, error: error.message };
        }
    }
}

// Create singleton instance
const databaseConnection = new DatabaseConnection();

module.exports = databaseConnection;
