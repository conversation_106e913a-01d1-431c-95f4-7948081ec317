/**
 * Test script for database migration and MongoDB integration
 * This script tests the database connection and runs the migration
 */

const chalk = require('chalk');
const databaseConnection = require('../config/database');
const ConfigMigration = require('../utils/migrationScript');
const configService = require('../services/configService');

async function testDatabaseMigration() {
    console.log(chalk.blue('🧪 Testing Database Migration'));
    console.log(chalk.blue('=============================='));

    try {
        // Step 1: Test database connection
        console.log(chalk.blue('\n1️⃣ Testing database connection...'));
        const connectionTest = await databaseConnection.testConnection();
        
        if (!connectionTest.success) {
            console.error(chalk.red('❌ Database connection failed:'), connectionTest.error);
            return;
        }
        
        console.log(chalk.green('✅ Database connection successful'));

        // Step 2: Run migration
        console.log(chalk.blue('\n2️⃣ Running configuration migration...'));
        const migration = new ConfigMigration();
        await migration.migrate();
        
        console.log(chalk.green('✅ Migration completed successfully'));

        // Step 3: Test config service
        console.log(chalk.blue('\n3️⃣ Testing config service...'));
        
        // Test getting shared config
        const sharedConfig = await configService.getSharedConfig();
        if (sharedConfig) {
            console.log(chalk.green('✅ Shared config retrieved successfully'));
            console.log(chalk.blue(`  - World Islands: ${Object.keys(sharedConfig.worldIslands).length} worlds`));
            console.log(chalk.blue(`  - Island Bosses: ${Object.keys(sharedConfig.islandBosses).length} islands`));
        } else {
            console.log(chalk.red('❌ Failed to retrieve shared config'));
        }

        // Test getting all configured servers
        const allServers = await configService.getAllConfiguredServers();
        console.log(chalk.green(`✅ Found ${allServers.length} configured servers`));

        // Test getting enabled services
        const dungeonServers = await configService.getEnabledDungeonServers();
        const worldBossServers = await configService.getEnabledWorldBossServers();
        const infernalServers = await configService.getEnabledInfernalServers();

        console.log(chalk.blue(`  - Dungeon alerts enabled: ${dungeonServers.length} servers`));
        console.log(chalk.blue(`  - World boss alerts enabled: ${worldBossServers.length} servers`));
        console.log(chalk.blue(`  - Infernal alerts enabled: ${infernalServers.length} servers`));

        // Step 4: Test cache functionality
        console.log(chalk.blue('\n4️⃣ Testing cache functionality...'));
        const cacheStats = configService.getCacheStats();
        console.log(chalk.green('✅ Cache stats retrieved:'));
        console.log(chalk.blue(`  - Server configs cached: ${cacheStats.serverConfigs}`));
        console.log(chalk.blue(`  - Shared config cached: ${cacheStats.sharedConfigCached}`));
        console.log(chalk.blue(`  - Cache timeout: ${cacheStats.cacheTimeout}ms`));

        // Step 5: Test specific server config
        console.log(chalk.blue('\n5️⃣ Testing specific server config retrieval...'));
        if (allServers.length > 0) {
            const testServerId = allServers[0].serverId;
            const serverConfig = await configService.getServerConfig(testServerId);
            
            if (serverConfig) {
                console.log(chalk.green(`✅ Retrieved config for ${serverConfig.name}`));
                console.log(chalk.blue(`  - Server ID: ${serverConfig.serverId}`));
                console.log(chalk.blue(`  - Dungeon alerts: ${serverConfig.dungeonAlert?.enabled ? 'Enabled' : 'Disabled'}`));
                console.log(chalk.blue(`  - World boss alerts: ${serverConfig.worldBossAlert?.enabled ? 'Enabled' : 'Disabled'}`));
                console.log(chalk.blue(`  - Infernal alerts: ${serverConfig.infernalAlert?.enabled ? 'Enabled' : 'Disabled'}`));
            } else {
                console.log(chalk.red('❌ Failed to retrieve server config'));
            }
        }

        console.log(chalk.green('\n🎉 All tests completed successfully!'));
        console.log(chalk.blue('\n📊 Migration Summary:'));
        console.log(chalk.blue(`  - Total servers migrated: ${allServers.length}`));
        console.log(chalk.blue(`  - Services enabled:`));
        console.log(chalk.blue(`    • Dungeon alerts: ${dungeonServers.length} servers`));
        console.log(chalk.blue(`    • World boss alerts: ${worldBossServers.length} servers`));
        console.log(chalk.blue(`    • Infernal alerts: ${infernalServers.length} servers`));

    } catch (error) {
        console.error(chalk.red('❌ Test failed:'), error);
    } finally {
        // Close database connection
        try {
            await databaseConnection.disconnect();
            console.log(chalk.green('\n✅ Database connection closed'));
        } catch (error) {
            console.error(chalk.red('❌ Error closing database:'), error);
        }
    }
}

// Run the test if this script is executed directly
if (require.main === module) {
    testDatabaseMigration()
        .then(() => {
            console.log(chalk.green('\n🎉 Database migration test completed!'));
            process.exit(0);
        })
        .catch((error) => {
            console.error(chalk.red('\n💥 Database migration test failed:'), error);
            process.exit(1);
        });
}

module.exports = testDatabaseMigration;
