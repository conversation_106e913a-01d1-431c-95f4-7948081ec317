const { 
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
    <PERSON><PERSON><PERSON><PERSON><PERSON>, 
    ActionRowBuilder, 
    ButtonBuilder, 
    ButtonStyle, 
    StringSelectMenuBuilder,
    ChannelType,
    PermissionFlagsBits 
} = require('discord.js');
const configService = require('../../../services/configService');
const chalk = require('chalk');

// Session management for multi-step setup
const setupSessions = new Map();
const SESSION_TIMEOUT = 10 * 60 * 1000; // 10 minutes

module.exports = {
    data: new SlashCommandBuilder()
        .setName('setup')
        .setDescription('Interactive setup for Arise Crossover features')
        .addSubcommand(subcommand =>
            subcommand
                .setName('arise_crossover')
                .setDescription('Set up Arise Crossover game features')
                .addStringOption(option =>
                    option.setName('feature')
                        .setDescription('Directly access a specific feature setup')
                        .addChoices(
                            { name: 'Auto Dungeons', value: 'auto_dungeons' },
                            { name: 'Auto World Boss (Coming Soon)', value: 'auto_worldboss' }
                        )
                        .setRequired(false)))
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageGuild),
    name: 'setup',
    category: 'Info',
    aliases: [],
    cooldown: 10,
    usage: '/setup',
    description: 'Interactive setup for Arise Crossover features',
    memberpermissions: [],
    botpermissions: [],
    requiredroles: [],
    requiredchannels: [],
    alloweduserids: [],
    minargs: 0,
    maxargs: 0,
    nsfw: false,
    OwnerOnly: false,
    ServerOwnerOnly: false,
    DevloperTeamOnly: false,
    async execute(interaction) {
        try {
            // Check permissions
            if (!interaction.member.permissions.has(PermissionFlagsBits.ManageGuild)) {
                return await interaction.reply({
                    content: '❌ You need the "Manage Server" permission to use this command.',
                    ephemeral: true
                });
            }

            const subcommand = interaction.options.getSubcommand();
            
            if (subcommand === 'arise_crossover') {
                const directFeature = interaction.options.getString('feature');
                
                if (directFeature) {
                    // Direct feature access
                    return await this.handleDirectFeatureAccess(interaction, directFeature);
                } else {
                    // Show main feature selection
                    return await this.showFeatureSelection(interaction);
                }
            }

        } catch (error) {
            console.error(chalk.red('❌ Error in setup command:'), error);
            
            const errorEmbed = new EmbedBuilder()
                .setColor('#ff0000')
                .setTitle('❌ Setup Error')
                .setDescription('An error occurred during setup. Please try again or contact support.')
                .setTimestamp();

            if (interaction.replied || interaction.deferred) {
                await interaction.editReply({ embeds: [errorEmbed], components: [] });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    },

    async showFeatureSelection(interaction) {
        // Check for existing session
        const existingSession = setupSessions.get(interaction.user.id);
        if (existingSession) {
            return await interaction.reply({
                content: '⚠️ You already have an active setup session. Please complete or cancel it first.',
                ephemeral: true
            });
        }

        // Check current configuration
        const currentConfig = await configService.getServerConfig(interaction.guild.id);
        
        const embed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle('🎮 Arise Crossover Setup')
            .setDescription('Welcome to the interactive setup for Arise Crossover features!\n\nSelect a feature to configure:')
            .addFields([
                {
                    name: '🌀 Auto Dungeons',
                    value: currentConfig?.dungeonAlert?.enabled 
                        ? '✅ Currently **Enabled**' 
                        : '❌ Currently **Disabled**',
                    inline: true
                },
                {
                    name: '🌍 Auto World Boss',
                    value: currentConfig?.worldBossAlert?.enabled 
                        ? '✅ Currently **Enabled**' 
                        : '🚧 **Coming Soon**',
                    inline: true
                }
            ])
            .setFooter({ text: 'Session will timeout in 10 minutes' })
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('setup_auto_dungeons')
                    .setLabel('🌀 Auto Dungeons')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('setup_auto_worldboss')
                    .setLabel('🌍 Auto World Boss')
                    .setStyle(ButtonStyle.Secondary)
                    .setDisabled(true), // Coming soon
                new ButtonBuilder()
                    .setCustomId('setup_cancel')
                    .setLabel('❌ Cancel')
                    .setStyle(ButtonStyle.Danger)
            );

        await interaction.reply({ embeds: [embed], components: [row], ephemeral: true });

        // Create session
        this.createSession(interaction.user.id, {
            step: 'feature_selection',
            guildId: interaction.guild.id,
            channelId: interaction.channel.id,
            startTime: Date.now()
        });
    },

    async handleDirectFeatureAccess(interaction, feature) {
        if (feature === 'auto_dungeons') {
            return await this.showAutoDungeonsSetup(interaction);
        } else if (feature === 'auto_worldboss') {
            return await interaction.reply({
                content: '🚧 Auto World Boss feature is coming soon! Please check back later.',
                ephemeral: true
            });
        }
    },

    async showAutoDungeonsSetup(interaction) {
        const currentConfig = await configService.getServerConfig(interaction.guild.id);
        const dungeonConfig = currentConfig?.dungeonAlert;

        const embed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle('🌀 Auto Dungeons Setup')
            .setDescription('Configure automatic dungeon alerts for your server.')
            .addFields([
                {
                    name: 'Current Status',
                    value: dungeonConfig?.enabled ? '✅ Enabled' : '❌ Disabled',
                    inline: true
                },
                {
                    name: 'Target Channel',
                    value: dungeonConfig?.targetChannelId 
                        ? `<#${dungeonConfig.targetChannelId}>` 
                        : 'Not configured',
                    inline: true
                }
            ])
            .setFooter({ text: 'Choose an option below to continue' })
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('dungeons_select_channel')
                    .setLabel('📢 Select Target Channel')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('dungeons_setup_roles')
                    .setLabel('🧱 Setup Ping Roles')
                    .setStyle(ButtonStyle.Secondary),
                new ButtonBuilder()
                    .setCustomId('dungeons_finish_setup')
                    .setLabel('✅ Finish Setup')
                    .setStyle(ButtonStyle.Success)
                    .setDisabled(!dungeonConfig?.targetChannelId),
                new ButtonBuilder()
                    .setCustomId('setup_back')
                    .setLabel('🔙 Back')
                    .setStyle(ButtonStyle.Secondary)
            );

        if (interaction.replied || interaction.deferred) {
            await interaction.editReply({ embeds: [embed], components: [row] });
        } else {
            await interaction.reply({ embeds: [embed], components: [row], ephemeral: true });
        }

        // Update or create session
        this.updateSession(interaction.user.id, {
            step: 'auto_dungeons_setup',
            guildId: interaction.guild.id,
            feature: 'auto_dungeons'
        });
    },

    createSession(userId, sessionData) {
        setupSessions.set(userId, sessionData);
        
        // Auto-cleanup session after timeout
        setTimeout(() => {
            setupSessions.delete(userId);
        }, SESSION_TIMEOUT);
    },

    updateSession(userId, updates) {
        const session = setupSessions.get(userId);
        if (session) {
            Object.assign(session, updates);
        } else {
            this.createSession(userId, updates);
        }
    },

    getSession(userId) {
        return setupSessions.get(userId);
    },

    clearSession(userId) {
        setupSessions.delete(userId);
    },

    // Helper method to get role color based on role type
    getRoleColor(roleKey) {
        const colors = {
            E: '#8897aa',      // Gray
            D: '#4488ff',      // Blue  
            C: '#44aaff',      // Light Blue
            B: '#6644ff',      // Purple
            A: '#8844ff',      // Dark Purple
            S: '#aa44ff',      // Magenta
            SS: '#ff44ff',     // Pink
            DUNGEON_PING: '#ffa500',     // Orange
            RED_DUNGEON: '#ff4444',      // Red
            DOUBLE_DUNGEON: '#44ff44'    // Green
        };
        return colors[roleKey] || '#7289da'; // Default Discord blurple
    },

    // Handle button interactions
    async handleButtonInteraction(interaction) {
        try {
            const session = this.getSession(interaction.user.id);
            if (!session) {
                return await interaction.reply({
                    content: '❌ No active setup session found. Please start a new setup.',
                    ephemeral: true
                });
            }

            const customId = interaction.customId;

            switch (customId) {
                case 'setup_auto_dungeons':
                    await this.showAutoDungeonsSetup(interaction);
                    break;

                case 'setup_cancel':
                    await this.handleCancel(interaction);
                    break;

                case 'setup_back':
                    await this.showFeatureSelection(interaction);
                    break;

                case 'dungeons_select_channel':
                    await this.showChannelSelection(interaction);
                    break;

                case 'dungeons_setup_roles':
                    await this.showRoleSetup(interaction);
                    break;

                case 'dungeons_finish_setup':
                    await this.finishDungeonSetup(interaction);
                    break;

                case 'dungeons_use_existing_channel':
                    await this.showExistingChannels(interaction);
                    break;

                case 'dungeons_create_new_channel':
                    await this.createNewChannel(interaction);
                    break;

                case 'dungeons_use_existing_roles':
                    await this.showRoleSelectionOptions(interaction);
                    break;

                case 'dungeons_create_new_roles':
                case 'dungeons_create_all_roles':
                    await this.createNewRoles(interaction);
                    break;

                case 'setup_rank_roles':
                    await this.setupRankRoles(interaction);
                    break;

                case 'setup_world_roles':
                    await this.setupWorldRoles(interaction);
                    break;

                case 'setup_island_roles':
                    await this.setupIslandRoles(interaction);
                    break;

                case 'setup_special_roles':
                    await this.setupSpecialRoles(interaction);
                    break;

                // Role creation handlers
                case 'create_rank_roles':
                case 'create_world_roles':
                case 'create_island_roles':
                case 'create_special_roles':
                    await this.createSpecificRoles(interaction, customId);
                    break;

                // Skip handlers
                case 'skip_rank_roles':
                case 'skip_world_roles':
                case 'skip_island_roles':
                case 'skip_special_roles':
                    await this.skipRoleSetup(interaction, customId);
                    break;

                default:
                    if (customId.startsWith('select_channel_')) {
                        await this.handleChannelSelection(interaction);
                    } else if (customId.startsWith('select_role_')) {
                        await this.handleRoleSelection(interaction);
                    }
                    break;
            }

        } catch (error) {
            console.error(chalk.red('❌ Error handling button interaction:'), error);
            await interaction.reply({
                content: '❌ An error occurred. Please try again.',
                ephemeral: true
            });
        }
    },

    async handleCancel(interaction) {
        this.clearSession(interaction.user.id);

        const embed = new EmbedBuilder()
            .setColor('#ff9900')
            .setTitle('🚫 Setup Cancelled')
            .setDescription('Setup has been cancelled. You can start a new setup anytime using `/setup arise_crossover`.')
            .setTimestamp();

        await interaction.update({ embeds: [embed], components: [] });
    },

    async showChannelSelection(interaction) {
        const embed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle('📢 Channel Selection')
            .setDescription('Choose how you want to set up the target channel for dungeon alerts:')
            .addFields([
                {
                    name: '🏷️ Use Existing Channel',
                    value: 'Select from your server\'s existing text channels',
                    inline: false
                },
                {
                    name: '🆕 Create New Channel',
                    value: 'Create a new #dungeons channel automatically',
                    inline: false
                }
            ])
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('dungeons_use_existing_channel')
                    .setLabel('🏷️ Use Existing Channel')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('dungeons_create_new_channel')
                    .setLabel('🆕 Create New Channel')
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('setup_back')
                    .setLabel('🔙 Back')
                    .setStyle(ButtonStyle.Secondary)
            );

        await interaction.update({ embeds: [embed], components: [row] });

        this.updateSession(interaction.user.id, { step: 'channel_selection' });
    },

    async showExistingChannels(interaction) {
        const channels = interaction.guild.channels.cache
            .filter(channel => channel.type === ChannelType.GuildText)
            .first(25); // Discord limit for select menu options

        if (channels.length === 0) {
            return await interaction.update({
                content: '❌ No text channels found. Please create a channel first.',
                embeds: [],
                components: []
            });
        }

        const options = channels.map(channel => ({
            label: `#${channel.name}`,
            value: channel.id,
            description: channel.topic ? channel.topic.substring(0, 100) : 'No description'
        }));

        const selectMenu = new StringSelectMenuBuilder()
            .setCustomId('select_channel_dropdown')
            .setPlaceholder('Choose a channel for dungeon alerts')
            .addOptions(options);

        const row = new ActionRowBuilder().addComponents(selectMenu);

        const backRow = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('setup_back')
                    .setLabel('🔙 Back')
                    .setStyle(ButtonStyle.Secondary)
            );

        const embed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle('🏷️ Select Existing Channel')
            .setDescription('Choose a text channel where dungeon alerts will be posted:')
            .setTimestamp();

        await interaction.update({
            embeds: [embed],
            components: [row, backRow]
        });
    },

    async createNewChannel(interaction) {
        try {
            // Check if bot has permission to create channels
            if (!interaction.guild.members.me.permissions.has(PermissionFlagsBits.ManageChannels)) {
                return await interaction.update({
                    content: '❌ I don\'t have permission to create channels. Please give me the "Manage Channels" permission or use an existing channel.',
                    embeds: [],
                    components: []
                });
            }

            // Create the dungeons channel
            const channel = await interaction.guild.channels.create({
                name: 'dungeons',
                type: ChannelType.GuildText,
                topic: 'Automatic dungeon alerts from Arise Crossover',
                reason: 'Created by setup command for dungeon alerts'
            });

            // Update session with selected channel
            this.updateSession(interaction.user.id, {
                selectedChannelId: channel.id,
                step: 'channel_selected'
            });

            const embed = new EmbedBuilder()
                .setColor('#00ff00')
                .setTitle('✅ Channel Created')
                .setDescription(`Successfully created ${channel} for dungeon alerts!`)
                .addFields([
                    {
                        name: 'Next Step',
                        value: 'Now you can set up ping roles or finish the setup.',
                        inline: false
                    }
                ])
                .setTimestamp();

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('dungeons_setup_roles')
                        .setLabel('🧱 Setup Ping Roles')
                        .setStyle(ButtonStyle.Primary),
                    new ButtonBuilder()
                        .setCustomId('dungeons_finish_setup')
                        .setLabel('✅ Finish Setup')
                        .setStyle(ButtonStyle.Success),
                    new ButtonBuilder()
                        .setCustomId('setup_back')
                        .setLabel('🔙 Back')
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.update({ embeds: [embed], components: [row] });

        } catch (error) {
            console.error(chalk.red('❌ Error creating channel:'), error);
            await interaction.update({
                content: '❌ Failed to create channel. Please check my permissions and try again.',
                embeds: [],
                components: []
            });
        }
    },

    async handleChannelSelection(interaction) {
        const channelId = interaction.values[0];
        const channel = interaction.guild.channels.cache.get(channelId);

        if (!channel) {
            return await interaction.update({
                content: '❌ Selected channel not found.',
                embeds: [],
                components: []
            });
        }

        // Update session with selected channel
        this.updateSession(interaction.user.id, {
            selectedChannelId: channelId,
            step: 'channel_selected'
        });

        const embed = new EmbedBuilder()
            .setColor('#00ff00')
            .setTitle('✅ Channel Selected')
            .setDescription(`Selected ${channel} for dungeon alerts!`)
            .addFields([
                {
                    name: 'Next Step',
                    value: 'Now you can set up ping roles or finish the setup.',
                    inline: false
                }
            ])
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('dungeons_setup_roles')
                    .setLabel('🧱 Setup Ping Roles')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('dungeons_finish_setup')
                    .setLabel('✅ Finish Setup')
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('setup_back')
                    .setLabel('🔙 Back')
                    .setStyle(ButtonStyle.Secondary)
            );

        await interaction.update({ embeds: [embed], components: [row] });
    },

    async showRoleSelectionOptions(interaction) {
        const embed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle('🎭 Role Selection Options')
            .setDescription('Choose which types of roles you want to set up:')
            .addFields([
                {
                    name: '🏆 Rank Roles (E-SS)',
                    value: 'Roles for different dungeon ranks',
                    inline: true
                },
                {
                    name: '🌍 World Roles',
                    value: 'World 1 and World 2 ping roles',
                    inline: true
                },
                {
                    name: '🏝️ Island Roles',
                    value: 'Individual island ping roles',
                    inline: true
                },
                {
                    name: '🔴 Special Roles',
                    value: 'Red Gate, Double Dungeon, General Ping',
                    inline: true
                }
            ])
            .setTimestamp();

        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('setup_rank_roles')
                    .setLabel('🏆 Rank Roles')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('setup_world_roles')
                    .setLabel('🌍 World Roles')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('setup_island_roles')
                    .setLabel('🏝️ Island Roles')
                    .setStyle(ButtonStyle.Primary)
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('setup_special_roles')
                    .setLabel('🔴 Special Roles')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('dungeons_create_all_roles')
                    .setLabel('🆕 Create All Roles')
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('setup_back')
                    .setLabel('🔙 Back')
                    .setStyle(ButtonStyle.Secondary)
            );

        await interaction.update({ embeds: [embed], components: [row1, row2] });

        this.updateSession(interaction.user.id, { step: 'role_selection_options' });
    },

    async showRoleSetup(interaction) {
        const embed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle('🧱 Role Setup')
            .setDescription('Choose how you want to set up ping roles for dungeon alerts:')
            .addFields([
                {
                    name: '🎭 Use Existing Roles',
                    value: 'Select from your server\'s existing roles',
                    inline: false
                },
                {
                    name: '🆕 Create New Roles',
                    value: 'Automatically create all necessary roles with proper colors',
                    inline: false
                }
            ])
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('dungeons_use_existing_roles')
                    .setLabel('🎭 Use Existing Roles')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('dungeons_create_new_roles')
                    .setLabel('🆕 Create New Roles')
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('setup_back')
                    .setLabel('🔙 Back')
                    .setStyle(ButtonStyle.Secondary)
            );

        await interaction.update({ embeds: [embed], components: [row] });

        this.updateSession(interaction.user.id, { step: 'role_setup' });
    },

    async createNewRoles(interaction) {
        try {
            // Check if bot has permission to create roles
            if (!interaction.guild.members.me.permissions.has(PermissionFlagsBits.ManageRoles)) {
                return await interaction.update({
                    content: '❌ I don\'t have permission to create roles. Please give me the "Manage Roles" permission or use existing roles.',
                    embeds: [],
                    components: []
                });
            }

            await interaction.update({
                content: '⏳ Creating roles... This may take a moment.',
                embeds: [],
                components: []
            });

            // Get shared config for island names
            const configWrapper = require('../../../services/configWrapper');
            const sharedConfig = await configWrapper.getSharedConfig();
            const allIslands = [
                ...(sharedConfig?.worldIslands?.[1] || []),
                ...(sharedConfig?.worldIslands?.[2] || [])
            ];

            const roleData = [
                // Rank roles
                { name: 'E Rank', key: 'E', type: 'dungeon', color: this.getRoleColor('E') },
                { name: 'D Rank', key: 'D', type: 'dungeon', color: this.getRoleColor('D') },
                { name: 'C Rank', key: 'C', type: 'dungeon', color: this.getRoleColor('C') },
                { name: 'B Rank', key: 'B', type: 'dungeon', color: this.getRoleColor('B') },
                { name: 'A Rank', key: 'A', type: 'dungeon', color: this.getRoleColor('A') },
                { name: 'S Rank', key: 'S', type: 'dungeon', color: this.getRoleColor('S') },
                { name: 'SS Rank', key: 'SS', type: 'dungeon', color: this.getRoleColor('SS') },
                // Special roles
                { name: 'Dungeon Ping', key: 'DUNGEON_PING', type: 'dungeon', color: this.getRoleColor('DUNGEON_PING') },
                { name: 'Red Dungeon', key: 'RED_DUNGEON', type: 'dungeon', color: this.getRoleColor('RED_DUNGEON') },
                { name: 'Double Dungeon', key: 'DOUBLE_DUNGEON', type: 'dungeon', color: this.getRoleColor('DOUBLE_DUNGEON') },
                // World roles
                { name: 'World 1', key: '1', type: 'world', color: '#00ff00' },
                { name: 'World 2', key: '2', type: 'world', color: '#ff0000' }
            ];

            // Add island roles
            allIslands.forEach(island => {
                roleData.push({
                    name: island,
                    key: island,
                    type: 'island',
                    color: '#3498db' // Blue color for island roles
                });
            });

            const createdRoles = {};
            const dungeonRoles = {};
            const worldRoles = {};
            const islandRoles = {};

            for (const roleInfo of roleData) {
                try {
                    const role = await interaction.guild.roles.create({
                        name: roleInfo.name,
                        color: roleInfo.color,
                        reason: 'Created by setup command for dungeon alerts'
                    });

                    createdRoles[roleInfo.key] = role.id;

                    if (roleInfo.type === 'world') {
                        worldRoles[roleInfo.key] = role.id;
                    } else if (roleInfo.type === 'island') {
                        islandRoles[roleInfo.key] = role.id;
                    } else {
                        dungeonRoles[roleInfo.key] = role.id;
                    }

                    // Small delay to avoid rate limits
                    await new Promise(resolve => setTimeout(resolve, 150));
                } catch (error) {
                    console.error(chalk.red(`❌ Failed to create role ${roleInfo.name}:`), error);
                }
            }

            // Update session with created roles
            this.updateSession(interaction.user.id, {
                dungeonRoles,
                worldRoles,
                islandRoles,
                step: 'roles_created'
            });

            const embed = new EmbedBuilder()
                .setColor('#00ff00')
                .setTitle('✅ Roles Created')
                .setDescription(`Successfully created ${Object.keys(createdRoles).length} roles for dungeon alerts!`)
                .addFields([
                    {
                        name: 'Rank Roles',
                        value: Object.entries(dungeonRoles)
                            .filter(([key]) => ['E', 'D', 'C', 'B', 'A', 'S', 'SS'].includes(key))
                            .map(([, roleId]) => `<@&${roleId}>`)
                            .join(', ') || 'None',
                        inline: false
                    },
                    {
                        name: 'Special Roles',
                        value: Object.entries(dungeonRoles)
                            .filter(([key]) => ['DUNGEON_PING', 'RED_DUNGEON', 'DOUBLE_DUNGEON'].includes(key))
                            .map(([, roleId]) => `<@&${roleId}>`)
                            .join(', ') || 'None',
                        inline: false
                    },
                    {
                        name: 'World & Island Roles',
                        value: [
                            ...Object.values(worldRoles).map(roleId => `<@&${roleId}>`),
                            ...Object.values(islandRoles).slice(0, 5).map(roleId => `<@&${roleId}>`)
                        ].join(', ') + (Object.keys(islandRoles).length > 5 ? ` +${Object.keys(islandRoles).length - 5} more` : ''),
                        inline: false
                    }
                ])
                .setTimestamp();

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('dungeons_finish_setup')
                        .setLabel('✅ Finish Setup')
                        .setStyle(ButtonStyle.Success),
                    new ButtonBuilder()
                        .setCustomId('setup_back')
                        .setLabel('🔙 Back')
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.editReply({ embeds: [embed], components: [row] });

        } catch (error) {
            console.error(chalk.red('❌ Error creating roles:'), error);
            await interaction.editReply({
                content: '❌ Failed to create roles. Please check my permissions and try again.',
                embeds: [],
                components: []
            });
        }
    },

    async finishDungeonSetup(interaction) {
        try {
            const session = this.getSession(interaction.user.id);
            if (!session) {
                return await interaction.update({
                    content: '❌ No active setup session found. Please start a new setup.',
                    embeds: [],
                    components: []
                });
            }

            // Check if channel is selected
            if (!session.selectedChannelId) {
                return await interaction.update({
                    content: '❌ Setup incomplete. Please select a channel first using the "Select Target Channel" option.',
                    embeds: [],
                    components: []
                });
            }

            // Import configService here to avoid circular dependency
            const configService = require('../../../services/configService');

            // Save configuration to database
            const configData = {
                serverId: session.guildId,
                name: interaction.guild.name,
                dungeonAlert: {
                    enabled: true,
                    targetChannelId: session.selectedChannelId,
                    dungeonRoles: session.dungeonRoles || {},
                    worldRoles: session.worldRoles || {},
                    islandRoles: session.islandRoles || {}
                },
                worldBossAlert: {
                    enabled: false
                },
                infernalAlert: {
                    enabled: false
                }
            };

            await configService.saveServerConfig(session.guildId, configData);

            // Count configured roles
            const roleCount = {
                dungeon: Object.keys(session.dungeonRoles || {}).length,
                world: Object.keys(session.worldRoles || {}).length,
                island: Object.keys(session.islandRoles || {}).length
            };

            // Clear session
            this.clearSession(interaction.user.id);

            const embed = new EmbedBuilder()
                .setColor('#00ff00')
                .setTitle('🎉 Setup Complete!')
                .setDescription('Auto Dungeons has been successfully configured for your server!')
                .addFields([
                    {
                        name: 'Target Channel',
                        value: `<#${session.selectedChannelId}>`,
                        inline: true
                    },
                    {
                        name: 'Status',
                        value: '✅ Enabled',
                        inline: true
                    },
                    {
                        name: 'Configured Roles',
                        value: `🏆 Dungeon: ${roleCount.dungeon}\n🌍 World: ${roleCount.world}\n🏝️ Island: ${roleCount.island}`,
                        inline: true
                    },
                    {
                        name: 'How It Works',
                        value: '• Dungeon alerts will be posted automatically\n• Users with matching roles will be pinged\n• Alerts work even without ping roles configured\n• Use `/setup arise_crossover` to modify settings',
                        inline: false
                    }
                ])
                .setFooter({
                    text: roleCount.dungeon === 0 && roleCount.world === 0 && roleCount.island === 0
                        ? 'Note: No ping roles configured - alerts will be posted without pings'
                        : 'Ping roles configured successfully'
                })
                .setTimestamp();

            await interaction.update({ embeds: [embed], components: [] });

        } catch (error) {
            console.error(chalk.red('❌ Error finishing setup:'), error);
            await interaction.update({
                content: '❌ Failed to save configuration. Please try again.',
                embeds: [],
                components: []
            });
        }
    },

    async setupRankRoles(interaction) {
        const embed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle('🏆 Rank Roles Setup')
            .setDescription('Choose how to set up rank roles (E, D, C, B, A, S, SS):')
            .addFields([
                {
                    name: 'Options',
                    value: '• **Create New**: Auto-create all rank roles with proper colors\n• **Use Existing**: Select from your server\'s existing roles\n• **Skip**: Continue without rank roles',
                    inline: false
                }
            ])
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('create_rank_roles')
                    .setLabel('🆕 Create New')
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('select_existing_rank_roles')
                    .setLabel('🎭 Use Existing')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('skip_rank_roles')
                    .setLabel('⏭️ Skip')
                    .setStyle(ButtonStyle.Secondary),
                new ButtonBuilder()
                    .setCustomId('setup_back')
                    .setLabel('🔙 Back')
                    .setStyle(ButtonStyle.Secondary)
            );

        await interaction.update({ embeds: [embed], components: [row] });
    },

    async setupWorldRoles(interaction) {
        const embed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle('🌍 World Roles Setup')
            .setDescription('Choose how to set up world ping roles (World 1, World 2):')
            .addFields([
                {
                    name: 'Options',
                    value: '• **Create New**: Auto-create world ping roles\n• **Use Existing**: Select from your server\'s existing roles\n• **Skip**: Continue without world roles',
                    inline: false
                }
            ])
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('create_world_roles')
                    .setLabel('🆕 Create New')
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('select_existing_world_roles')
                    .setLabel('🎭 Use Existing')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('skip_world_roles')
                    .setLabel('⏭️ Skip')
                    .setStyle(ButtonStyle.Secondary),
                new ButtonBuilder()
                    .setCustomId('setup_back')
                    .setLabel('🔙 Back')
                    .setStyle(ButtonStyle.Secondary)
            );

        await interaction.update({ embeds: [embed], components: [row] });
    },

    async setupIslandRoles(interaction) {
        const embed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle('🏝️ Island Roles Setup')
            .setDescription('Choose how to set up individual island ping roles:')
            .addFields([
                {
                    name: 'Options',
                    value: '• **Create New**: Auto-create roles for all islands\n• **Use Existing**: Select from your server\'s existing roles\n• **Skip**: Continue without island roles',
                    inline: false
                }
            ])
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('create_island_roles')
                    .setLabel('🆕 Create New')
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('select_existing_island_roles')
                    .setLabel('🎭 Use Existing')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('skip_island_roles')
                    .setLabel('⏭️ Skip')
                    .setStyle(ButtonStyle.Secondary),
                new ButtonBuilder()
                    .setCustomId('setup_back')
                    .setLabel('🔙 Back')
                    .setStyle(ButtonStyle.Secondary)
            );

        await interaction.update({ embeds: [embed], components: [row] });
    },

    async setupSpecialRoles(interaction) {
        const embed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle('🔴 Special Roles Setup')
            .setDescription('Choose how to set up special ping roles:')
            .addFields([
                {
                    name: 'Special Roles Include',
                    value: '• **Dungeon Ping**: General dungeon notifications\n• **Red Gate**: Red dungeon alerts\n• **Double Dungeon**: Double dungeon alerts',
                    inline: false
                },
                {
                    name: 'Options',
                    value: '• **Create New**: Auto-create all special roles\n• **Use Existing**: Select from your server\'s existing roles\n• **Skip**: Continue without special roles',
                    inline: false
                }
            ])
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('create_special_roles')
                    .setLabel('🆕 Create New')
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('select_existing_special_roles')
                    .setLabel('🎭 Use Existing')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('skip_special_roles')
                    .setLabel('⏭️ Skip')
                    .setStyle(ButtonStyle.Secondary),
                new ButtonBuilder()
                    .setCustomId('setup_back')
                    .setLabel('🔙 Back')
                    .setStyle(ButtonStyle.Secondary)
            );

        await interaction.update({ embeds: [embed], components: [row] });
    },

    async createSpecificRoles(interaction, roleType) {
        try {
            // Check if bot has permission to create roles
            if (!interaction.guild.members.me.permissions.has(PermissionFlagsBits.ManageRoles)) {
                return await interaction.update({
                    content: '❌ I don\'t have permission to create roles. Please give me the "Manage Roles" permission.',
                    embeds: [],
                    components: []
                });
            }

            await interaction.update({
                content: '⏳ Creating roles... This may take a moment.',
                embeds: [],
                components: []
            });

            const session = this.getSession(interaction.user.id);
            const currentRoles = {
                dungeonRoles: session?.dungeonRoles || {},
                worldRoles: session?.worldRoles || {},
                islandRoles: session?.islandRoles || {}
            };

            let rolesToCreate = [];
            let roleCategory = '';

            switch (roleType) {
                case 'create_rank_roles':
                    roleCategory = 'Rank Roles';
                    rolesToCreate = [
                        { name: 'E Rank', key: 'E', type: 'dungeon', color: this.getRoleColor('E') },
                        { name: 'D Rank', key: 'D', type: 'dungeon', color: this.getRoleColor('D') },
                        { name: 'C Rank', key: 'C', type: 'dungeon', color: this.getRoleColor('C') },
                        { name: 'B Rank', key: 'B', type: 'dungeon', color: this.getRoleColor('B') },
                        { name: 'A Rank', key: 'A', type: 'dungeon', color: this.getRoleColor('A') },
                        { name: 'S Rank', key: 'S', type: 'dungeon', color: this.getRoleColor('S') },
                        { name: 'SS Rank', key: 'SS', type: 'dungeon', color: this.getRoleColor('SS') }
                    ];
                    break;

                case 'create_world_roles':
                    roleCategory = 'World Roles';
                    rolesToCreate = [
                        { name: 'World 1', key: '1', type: 'world', color: '#00ff00' },
                        { name: 'World 2', key: '2', type: 'world', color: '#ff0000' }
                    ];
                    break;

                case 'create_special_roles':
                    roleCategory = 'Special Roles';
                    rolesToCreate = [
                        { name: 'Dungeon Ping', key: 'DUNGEON_PING', type: 'dungeon', color: this.getRoleColor('DUNGEON_PING') },
                        { name: 'Red Dungeon', key: 'RED_DUNGEON', type: 'dungeon', color: this.getRoleColor('RED_DUNGEON') },
                        { name: 'Double Dungeon', key: 'DOUBLE_DUNGEON', type: 'dungeon', color: this.getRoleColor('DOUBLE_DUNGEON') }
                    ];
                    break;

                case 'create_island_roles':
                    roleCategory = 'Island Roles';
                    const configWrapper = require('../../../services/configWrapper');
                    const sharedConfig = await configWrapper.getSharedConfig();
                    const allIslands = [
                        ...(sharedConfig?.worldIslands?.[1] || []),
                        ...(sharedConfig?.worldIslands?.[2] || [])
                    ];
                    rolesToCreate = allIslands.map(island => ({
                        name: island,
                        key: island,
                        type: 'island',
                        color: '#3498db'
                    }));
                    break;
            }

            const createdRoles = [];

            for (const roleInfo of rolesToCreate) {
                try {
                    const role = await interaction.guild.roles.create({
                        name: roleInfo.name,
                        color: roleInfo.color,
                        reason: `Created by setup command for ${roleCategory.toLowerCase()}`
                    });

                    createdRoles.push({ name: roleInfo.name, id: role.id });

                    if (roleInfo.type === 'world') {
                        currentRoles.worldRoles[roleInfo.key] = role.id;
                    } else if (roleInfo.type === 'island') {
                        currentRoles.islandRoles[roleInfo.key] = role.id;
                    } else {
                        currentRoles.dungeonRoles[roleInfo.key] = role.id;
                    }

                    // Small delay to avoid rate limits
                    await new Promise(resolve => setTimeout(resolve, 150));
                } catch (error) {
                    console.error(chalk.red(`❌ Failed to create role ${roleInfo.name}:`), error);
                }
            }

            // Update session with created roles
            this.updateSession(interaction.user.id, currentRoles);

            const embed = new EmbedBuilder()
                .setColor('#00ff00')
                .setTitle(`✅ ${roleCategory} Created`)
                .setDescription(`Successfully created ${createdRoles.length} ${roleCategory.toLowerCase()}!`)
                .addFields([
                    {
                        name: 'Created Roles',
                        value: createdRoles.map(role => `<@&${role.id}>`).join(', ') || 'None',
                        inline: false
                    }
                ])
                .setTimestamp();

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('dungeons_setup_roles')
                        .setLabel('🧱 Setup More Roles')
                        .setStyle(ButtonStyle.Primary),
                    new ButtonBuilder()
                        .setCustomId('dungeons_finish_setup')
                        .setLabel('✅ Finish Setup')
                        .setStyle(ButtonStyle.Success),
                    new ButtonBuilder()
                        .setCustomId('setup_back')
                        .setLabel('🔙 Back')
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.editReply({ embeds: [embed], components: [row] });

        } catch (error) {
            console.error(chalk.red('❌ Error creating specific roles:'), error);
            await interaction.editReply({
                content: '❌ Failed to create roles. Please check my permissions and try again.',
                embeds: [],
                components: []
            });
        }
    },

    async skipRoleSetup(interaction, skipType) {
        const roleTypeMap = {
            'skip_rank_roles': 'Rank Roles',
            'skip_world_roles': 'World Roles',
            'skip_island_roles': 'Island Roles',
            'skip_special_roles': 'Special Roles'
        };

        const skippedType = roleTypeMap[skipType] || 'Roles';

        const embed = new EmbedBuilder()
            .setColor('#ffa500')
            .setTitle(`⏭️ ${skippedType} Skipped`)
            .setDescription(`${skippedType} setup has been skipped. You can set them up later or continue with the current configuration.`)
            .addFields([
                {
                    name: 'Note',
                    value: 'Dungeon alerts will still work without ping roles - they just won\'t ping specific users.',
                    inline: false
                }
            ])
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('dungeons_setup_roles')
                    .setLabel('🧱 Setup Other Roles')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('dungeons_finish_setup')
                    .setLabel('✅ Finish Setup')
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('setup_back')
                    .setLabel('🔙 Back')
                    .setStyle(ButtonStyle.Secondary)
            );

        await interaction.update({ embeds: [embed], components: [row] });
    },
};

// Clean up expired sessions periodically
setInterval(() => {
    const now = Date.now();
    for (const [userId, session] of setupSessions.entries()) {
        if (now - session.startTime > SESSION_TIMEOUT) {
            setupSessions.delete(userId);
        }
    }
}, 60000); // Check every minute
