/**
 * Test script for the enhanced setup command system
 * This script tests the new MongoDB-based setup command functionality
 */

const chalk = require('chalk');
const databaseConnection = require('../config/database');
const configService = require('../services/configService');
const configWrapper = require('../services/configWrapper');

async function testEnhancedSetup() {
    console.log(chalk.blue('🧪 Testing Enhanced Setup Command System'));
    console.log(chalk.blue('========================================='));

    try {
        // Step 1: Test database connection
        console.log(chalk.blue('\n1️⃣ Testing database connection...'));
        await databaseConnection.connect();
        console.log(chalk.green('✅ Database connection established'));

        // Step 2: Test config wrapper functionality
        console.log(chalk.blue('\n2️⃣ Testing config wrapper...'));
        
        console.log(chalk.blue('  Testing database config source...'));
        const configSource = configWrapper.getConfigSource();
        console.log(chalk.green(`✅ Config source: ${configSource}`));

        // Test getting shared config
        const sharedConfig = await configWrapper.getSharedConfig();
        if (sharedConfig) {
            console.log(chalk.green('✅ Shared config retrieved successfully'));
            console.log(chalk.blue(`  - World Islands: ${Object.keys(sharedConfig.worldIslands || {}).length} worlds`));
            console.log(chalk.blue(`  - Island Bosses: ${Object.keys(sharedConfig.islandBosses || {}).length} islands`));
        }

        // Test getting enabled servers
        const dungeonServers = await configWrapper.getEnabledDungeonServers();
        const worldBossServers = await configWrapper.getEnabledWorldBossServers();
        const infernalServers = await configWrapper.getEnabledInfernalServers();

        console.log(chalk.green(`✅ Retrieved enabled servers:`));
        console.log(chalk.blue(`  - Dungeon alerts: ${dungeonServers.length} servers`));
        console.log(chalk.blue(`  - World boss alerts: ${worldBossServers.length} servers`));
        console.log(chalk.blue(`  - Infernal alerts: ${infernalServers.length} servers`));

        // Step 3: Test creating a new server configuration
        console.log(chalk.blue('\n3️⃣ Testing server configuration creation...'));
        
        const testServerId = '999999999999999999'; // Test server ID
        const testServerName = 'Test Server for Enhanced Setup';
        
        // Check if test server already exists
        const existingConfig = await configWrapper.getServerConfig(testServerId);
        if (existingConfig) {
            console.log(chalk.yellow('⚠️ Test server config already exists, updating...'));
        }

        // Create test configuration
        const testConfigData = {
            serverId: testServerId,
            name: testServerName,
            dungeonAlert: {
                enabled: true,
                targetChannelId: '123456789012345678',
                dungeonRoles: {
                    E: '111111111111111111',
                    D: '222222222222222222',
                    C: '333333333333333333',
                    B: '444444444444444444',
                    A: '555555555555555555',
                    S: '666666666666666666',
                    SS: '777777777777777777',
                    DUNGEON_PING: '888888888888888888',
                    RED_DUNGEON: '999999999999999999',
                    DOUBLE_DUNGEON: '101010101010101010'
                },
                worldRoles: {
                    1: '111111111111111112',
                    2: '111111111111111113'
                },
                islandRoles: {}
            },
            worldBossAlert: {
                enabled: false
            },
            infernalAlert: {
                enabled: false
            }
        };

        try {
            const savedConfig = await configWrapper.saveServerConfig(testServerId, testConfigData);
            console.log(chalk.green(`✅ Test server configuration saved successfully`));
            console.log(chalk.blue(`  - Server: ${savedConfig.name}`));
            console.log(chalk.blue(`  - Dungeon alerts: ${savedConfig.dungeonAlert.enabled ? 'Enabled' : 'Disabled'}`));
        } catch (error) {
            console.error(chalk.red('❌ Failed to save test configuration:'), error);
        }

        // Step 4: Test retrieving the created configuration
        console.log(chalk.blue('\n4️⃣ Testing configuration retrieval...'));
        
        const retrievedConfig = await configWrapper.getServerConfig(testServerId);
        if (retrievedConfig) {
            console.log(chalk.green('✅ Test configuration retrieved successfully'));
            console.log(chalk.blue(`  - Server ID: ${retrievedConfig.serverId}`));
            console.log(chalk.blue(`  - Server Name: ${retrievedConfig.name}`));
            console.log(chalk.blue(`  - Target Channel: ${retrievedConfig.dungeonAlert?.targetChannelId}`));
            console.log(chalk.blue(`  - Dungeon Roles: ${Object.keys(retrievedConfig.dungeonAlert?.dungeonRoles || {}).length} roles`));
        } else {
            console.log(chalk.red('❌ Failed to retrieve test configuration'));
        }

        // Step 5: Test setup command file structure
        console.log(chalk.blue('\n5️⃣ Testing setup command files...'));
        
        const fs = require('fs');
        const setupCommandExists = fs.existsSync('./commands/slashCommands/info/setup-arise-crossover.js');
        const interactionHandlerExists = fs.existsSync('./events/interactionCreate.js');
        
        console.log(chalk.green(`✅ Setup command file: ${setupCommandExists ? 'Found' : 'Missing'}`));
        console.log(chalk.green(`✅ Interaction handler: ${interactionHandlerExists ? 'Found' : 'Missing'}`));

        // Step 6: Test fallback to file config
        console.log(chalk.blue('\n6️⃣ Testing fallback to file config...'));
        
        // Temporarily disable database config
        configWrapper.useDatabaseConfig = false;
        
        const fileConfigServers = await configWrapper.getEnabledDungeonServers();
        console.log(chalk.green(`✅ File config fallback works: ${fileConfigServers.length} servers`));
        console.log(chalk.blue(`  - Config source: ${configWrapper.getConfigSource()}`));
        
        // Reset to database config
        configWrapper.resetToDatabaseConfig();
        console.log(chalk.blue(`  - Reset to database config: ${configWrapper.getConfigSource()}`));

        // Step 7: Clean up test data
        console.log(chalk.blue('\n7️⃣ Cleaning up test data...'));
        
        try {
            await configService.deleteServerConfig(testServerId);
            console.log(chalk.green('✅ Test server configuration deleted'));
        } catch (error) {
            console.log(chalk.yellow('⚠️ Test cleanup failed (this is okay):'), error.message);
        }

        console.log(chalk.green('\n🎉 All enhanced setup tests completed successfully!'));
        
        // Final summary
        console.log(chalk.blue('\n📊 Enhanced Setup Test Summary:'));
        console.log(chalk.blue(`  - Database connection: ✅ Working`));
        console.log(chalk.blue(`  - Config wrapper: ✅ Working`));
        console.log(chalk.blue(`  - Configuration CRUD: ✅ Working`));
        console.log(chalk.blue(`  - Setup command files: ✅ Present`));
        console.log(chalk.blue(`  - File config fallback: ✅ Working`));
        console.log(chalk.blue(`  - Enhanced setup ready: ✅ Ready for use`));

        console.log(chalk.green('\n🚀 The enhanced setup command is ready to use!'));
        console.log(chalk.blue('   Use /setup arise_crossover to access the new interactive setup'));

    } catch (error) {
        console.error(chalk.red('❌ Enhanced setup test failed:'), error);
    } finally {
        // Close database connection
        try {
            await databaseConnection.disconnect();
            console.log(chalk.green('\n✅ Database connection closed'));
        } catch (error) {
            console.error(chalk.red('❌ Error closing database:'), error);
        }
    }
}

// Run the test if this script is executed directly
if (require.main === module) {
    testEnhancedSetup()
        .then(() => {
            console.log(chalk.green('\n🎉 Enhanced setup test completed!'));
            process.exit(0);
        })
        .catch((error) => {
            console.error(chalk.red('\n💥 Enhanced setup test failed:'), error);
            process.exit(1);
        });
}

module.exports = testEnhancedSetup;
