const chalk = require('chalk');
const configService = require('./configService');

/**
 * Configuration wrapper that provides backward compatibility
 * This service tries to use MongoDB first, falls back to file-based config if needed
 */
class ConfigWrapper {
    constructor() {
        this.useDatabaseConfig = true;
        this.fileConfigCache = null;
        this.fileConfigLoaded = false;
    }

    // Load file-based config as fallback
    loadFileConfig() {
        if (this.fileConfigLoaded) {
            return this.fileConfigCache;
        }

        try {
            // Clear cache and reload serverConfigs to get latest configuration
            delete require.cache[require.resolve('../config/serverConfigs')];
            this.fileConfigCache = require('../config/serverConfigs');
            this.fileConfigLoaded = true;
            return this.fileConfigCache;
        } catch (error) {
            console.error(chalk.red('❌ Error loading file config:'), error);
            return null;
        }
    }

    // Helper method to get server configuration
    async getServerConfig(serverId) {
        if (this.useDatabaseConfig) {
            try {
                return await configService.getServerConfig(serverId);
            } catch (error) {
                console.error(chalk.yellow('⚠️ Database config failed, falling back to file:'), error);
                this.useDatabaseConfig = false;
            }
        }

        // Fallback to file-based config
        const fileConfig = this.loadFileConfig();
        return fileConfig ? fileConfig.getServerConfig(serverId) : null;
    }

    // Helper method to get dungeon alert config for a server
    async getDungeonConfig(serverId) {
        if (this.useDatabaseConfig) {
            try {
                return await configService.getDungeonConfig(serverId);
            } catch (error) {
                console.error(chalk.yellow('⚠️ Database config failed, falling back to file:'), error);
                this.useDatabaseConfig = false;
            }
        }

        // Fallback to file-based config
        const fileConfig = this.loadFileConfig();
        return fileConfig ? fileConfig.getDungeonConfig(serverId) : null;
    }

    // Helper method to get world boss alert config for a server
    async getWorldBossConfig(serverId) {
        if (this.useDatabaseConfig) {
            try {
                return await configService.getWorldBossConfig(serverId);
            } catch (error) {
                console.error(chalk.yellow('⚠️ Database config failed, falling back to file:'), error);
                this.useDatabaseConfig = false;
            }
        }

        // Fallback to file-based config
        const fileConfig = this.loadFileConfig();
        return fileConfig ? fileConfig.getWorldBossConfig(serverId) : null;
    }

    // Helper method to get infernal alert config for a server
    async getInfernalConfig(serverId) {
        if (this.useDatabaseConfig) {
            try {
                return await configService.getInfernalConfig(serverId);
            } catch (error) {
                console.error(chalk.yellow('⚠️ Database config failed, falling back to file:'), error);
                this.useDatabaseConfig = false;
            }
        }

        // Fallback to file-based config
        const fileConfig = this.loadFileConfig();
        return fileConfig ? fileConfig.getInfernalConfig(serverId) : null;
    }

    // Helper method to get all enabled servers for dungeon alerts
    async getEnabledDungeonServers() {
        if (this.useDatabaseConfig) {
            try {
                return await configService.getEnabledDungeonServers();
            } catch (error) {
                console.error(chalk.yellow('⚠️ Database config failed, falling back to file:'), error);
                this.useDatabaseConfig = false;
            }
        }

        // Fallback to file-based config
        const fileConfig = this.loadFileConfig();
        return fileConfig ? fileConfig.getEnabledDungeonServers() : [];
    }

    // Helper method to get all enabled servers for world boss alerts
    async getEnabledWorldBossServers() {
        if (this.useDatabaseConfig) {
            try {
                return await configService.getEnabledWorldBossServers();
            } catch (error) {
                console.error(chalk.yellow('⚠️ Database config failed, falling back to file:'), error);
                this.useDatabaseConfig = false;
            }
        }

        // Fallback to file-based config
        const fileConfig = this.loadFileConfig();
        return fileConfig ? fileConfig.getEnabledWorldBossServers() : [];
    }

    // Helper method to get all enabled servers for infernal alerts
    async getEnabledInfernalServers() {
        if (this.useDatabaseConfig) {
            try {
                return await configService.getEnabledInfernalServers();
            } catch (error) {
                console.error(chalk.yellow('⚠️ Database config failed, falling back to file:'), error);
                this.useDatabaseConfig = false;
            }
        }

        // Fallback to file-based config
        const fileConfig = this.loadFileConfig();
        return fileConfig ? fileConfig.getEnabledInfernalServers() : [];
    }

    // Helper method to check if a server has any configuration
    async hasServerConfig(serverId) {
        if (this.useDatabaseConfig) {
            try {
                return await configService.hasServerConfig(serverId);
            } catch (error) {
                console.error(chalk.yellow('⚠️ Database config failed, falling back to file:'), error);
                this.useDatabaseConfig = false;
            }
        }

        // Fallback to file-based config
        const fileConfig = this.loadFileConfig();
        return fileConfig ? fileConfig.hasServerConfig(serverId) : false;
    }

    // Helper method to get all configured server IDs
    async getAllConfiguredServers() {
        if (this.useDatabaseConfig) {
            try {
                return await configService.getAllConfiguredServers();
            } catch (error) {
                console.error(chalk.yellow('⚠️ Database config failed, falling back to file:'), error);
                this.useDatabaseConfig = false;
            }
        }

        // Fallback to file-based config
        const fileConfig = this.loadFileConfig();
        return fileConfig ? fileConfig.getAllConfiguredServers() : [];
    }

    // Method to get shared configuration
    async getSharedConfig() {
        if (this.useDatabaseConfig) {
            try {
                const sharedConfig = await configService.getSharedConfig();
                // Convert MongoDB document to plain object for compatibility
                return sharedConfig ? sharedConfig.toObject() : null;
            } catch (error) {
                console.error(chalk.yellow('⚠️ Database config failed, falling back to file:'), error);
                this.useDatabaseConfig = false;
            }
        }

        // Fallback to file-based config
        const fileConfig = this.loadFileConfig();
        return fileConfig ? fileConfig.shared : null;
    }

    // Method to save server configuration (only works with database)
    async saveServerConfig(serverId, configData) {
        if (this.useDatabaseConfig) {
            try {
                return await configService.saveServerConfig(serverId, configData);
            } catch (error) {
                console.error(chalk.red('❌ Failed to save server config to database:'), error);
                throw error;
            }
        } else {
            throw new Error('Cannot save configuration - database not available and file-based config is read-only');
        }
    }

    // Method to get current configuration source
    getConfigSource() {
        return this.useDatabaseConfig ? 'database' : 'file';
    }

    // Method to force refresh file config cache
    refreshFileConfig() {
        this.fileConfigLoaded = false;
        this.fileConfigCache = null;
    }

    // Method to reset to database config
    resetToDatabaseConfig() {
        this.useDatabaseConfig = true;
    }
}

// Create singleton instance
const configWrapper = new ConfigWrapper();

module.exports = configWrapper;
