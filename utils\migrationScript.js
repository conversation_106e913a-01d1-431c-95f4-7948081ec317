const chalk = require('chalk');
const fs = require('fs');
const path = require('path');
const databaseConnection = require('../config/database');
const ServerConfig = require('../models/ServerConfig');
const SharedConfig = require('../models/SharedConfig');
const configService = require('../services/configService');

class ConfigMigration {
    constructor() {
        this.migrationComplete = false;
        this.backupCreated = false;
    }

    async migrate() {
        try {
            console.log(chalk.blue('🔄 Starting configuration migration from file to MongoDB...'));
            
            // Connect to database
            await databaseConnection.connect();
            
            // Create backup of current file
            await this.createBackup();
            
            // Load current serverConfigs.js
            const currentConfig = await this.loadCurrentConfig();
            
            if (!currentConfig) {
                console.log(chalk.yellow('⚠️ No current configuration found to migrate'));
                return;
            }
            
            // Migrate shared configuration
            await this.migrateSharedConfig(currentConfig.shared);
            
            // Migrate server configurations
            await this.migrateServerConfigs(currentConfig);
            
            // Verify migration
            await this.verifyMigration();
            
            console.log(chalk.green('✅ Configuration migration completed successfully!'));
            this.migrationComplete = true;
            
        } catch (error) {
            console.error(chalk.red('❌ Migration failed:'), error);
            throw error;
        }
    }

    async createBackup() {
        try {
            const configPath = path.join(__dirname, '../config/serverConfigs.js');
            const backupPath = path.join(__dirname, '../config/serverConfigs.backup.js');
            
            if (fs.existsSync(configPath)) {
                fs.copyFileSync(configPath, backupPath);
                console.log(chalk.green('✅ Created backup of serverConfigs.js'));
                this.backupCreated = true;
            } else {
                console.log(chalk.yellow('⚠️ serverConfigs.js not found, skipping backup'));
            }
        } catch (error) {
            console.error(chalk.red('❌ Failed to create backup:'), error);
            throw error;
        }
    }

    async loadCurrentConfig() {
        try {
            const configPath = path.join(__dirname, '../config/serverConfigs.js');
            
            if (!fs.existsSync(configPath)) {
                return null;
            }
            
            // Clear require cache to get fresh config
            delete require.cache[require.resolve('../config/serverConfigs.js')];
            const currentConfig = require('../config/serverConfigs.js');
            
            console.log(chalk.blue('📄 Loaded current configuration from file'));
            return currentConfig;
            
        } catch (error) {
            console.error(chalk.red('❌ Failed to load current config:'), error);
            throw error;
        }
    }

    async migrateSharedConfig(sharedConfig) {
        try {
            if (!sharedConfig) {
                console.log(chalk.yellow('⚠️ No shared configuration found'));
                return;
            }
            
            console.log(chalk.blue('🔄 Migrating shared configuration...'));
            
            // Check if shared config already exists
            let existingSharedConfig = await SharedConfig.findOne({ configType: 'shared' });
            
            if (existingSharedConfig) {
                console.log(chalk.yellow('⚠️ Shared configuration already exists, updating...'));
                Object.assign(existingSharedConfig, {
                    worldIslands: sharedConfig.worldIslands,
                    islandBosses: sharedConfig.islandBosses,
                    rankColors: sharedConfig.rankColors,
                    worldBossColors: sharedConfig.worldBossColors,
                    worldBossMapping: sharedConfig.worldBossMapping
                });
                await existingSharedConfig.save();
            } else {
                const newSharedConfig = new SharedConfig({
                    configType: 'shared',
                    worldIslands: sharedConfig.worldIslands,
                    islandBosses: sharedConfig.islandBosses,
                    rankColors: sharedConfig.rankColors,
                    worldBossColors: sharedConfig.worldBossColors,
                    worldBossMapping: sharedConfig.worldBossMapping
                });
                await newSharedConfig.save();
            }
            
            console.log(chalk.green('✅ Shared configuration migrated successfully'));
            
        } catch (error) {
            console.error(chalk.red('❌ Failed to migrate shared config:'), error);
            throw error;
        }
    }

    async migrateServerConfigs(currentConfig) {
        try {
            console.log(chalk.blue('🔄 Migrating server configurations...'));
            
            let migratedCount = 0;
            let skippedCount = 0;
            
            for (const [serverId, config] of Object.entries(currentConfig)) {
                // Skip non-server entries (shared, helper functions, etc.)
                if (typeof config !== 'object' || !config.name || serverId === 'shared') {
                    continue;
                }
                
                try {
                    // Check if server config already exists
                    const existingConfig = await ServerConfig.findByServerId(serverId);
                    
                    if (existingConfig) {
                        console.log(chalk.yellow(`⚠️ Server ${config.name} (${serverId}) already exists, updating...`));
                        
                        // Update existing configuration
                        Object.assign(existingConfig, {
                            name: config.name,
                            dungeonAlert: config.dungeonAlert || { enabled: false },
                            worldBossAlert: config.worldBossAlert || { enabled: false },
                            infernalAlert: config.infernalAlert || { enabled: false }
                        });
                        
                        await existingConfig.save();
                        skippedCount++;
                    } else {
                        // Create new server configuration
                        const newServerConfig = new ServerConfig({
                            serverId,
                            name: config.name,
                            dungeonAlert: config.dungeonAlert || { enabled: false },
                            worldBossAlert: config.worldBossAlert || { enabled: false },
                            infernalAlert: config.infernalAlert || { enabled: false }
                        });
                        
                        await newServerConfig.save();
                        migratedCount++;
                    }
                    
                    console.log(chalk.green(`✅ Migrated ${config.name} (${serverId})`));
                    
                } catch (error) {
                    console.error(chalk.red(`❌ Failed to migrate ${config.name} (${serverId}):`), error);
                }
            }
            
            console.log(chalk.green(`✅ Server configuration migration completed:`));
            console.log(chalk.blue(`  - New configurations: ${migratedCount}`));
            console.log(chalk.blue(`  - Updated configurations: ${skippedCount}`));
            
        } catch (error) {
            console.error(chalk.red('❌ Failed to migrate server configs:'), error);
            throw error;
        }
    }

    async verifyMigration() {
        try {
            console.log(chalk.blue('🔍 Verifying migration...'));
            
            // Check shared config
            const sharedConfig = await configService.getSharedConfig();
            if (!sharedConfig) {
                throw new Error('Shared configuration not found after migration');
            }
            
            // Check server configs
            const allServers = await configService.getAllConfiguredServers();
            console.log(chalk.green(`✅ Found ${allServers.length} server configurations`));
            
            // Check enabled services
            const dungeonServers = await configService.getEnabledDungeonServers();
            const worldBossServers = await configService.getEnabledWorldBossServers();
            const infernalServers = await configService.getEnabledInfernalServers();
            
            console.log(chalk.blue(`📊 Migration verification results:`));
            console.log(chalk.blue(`  - Total servers: ${allServers.length}`));
            console.log(chalk.blue(`  - Dungeon alerts enabled: ${dungeonServers.length}`));
            console.log(chalk.blue(`  - World boss alerts enabled: ${worldBossServers.length}`));
            console.log(chalk.blue(`  - Infernal alerts enabled: ${infernalServers.length}`));
            
        } catch (error) {
            console.error(chalk.red('❌ Migration verification failed:'), error);
            throw error;
        }
    }

    async rollback() {
        try {
            if (!this.backupCreated) {
                console.log(chalk.yellow('⚠️ No backup available for rollback'));
                return;
            }
            
            console.log(chalk.blue('🔄 Rolling back migration...'));
            
            const configPath = path.join(__dirname, '../config/serverConfigs.js');
            const backupPath = path.join(__dirname, '../config/serverConfigs.backup.js');
            
            if (fs.existsSync(backupPath)) {
                fs.copyFileSync(backupPath, configPath);
                console.log(chalk.green('✅ Rollback completed - serverConfigs.js restored from backup'));
            } else {
                console.log(chalk.red('❌ Backup file not found'));
            }
            
        } catch (error) {
            console.error(chalk.red('❌ Rollback failed:'), error);
            throw error;
        }
    }
}

// Export for use in other scripts
module.exports = ConfigMigration;

// Allow running this script directly
if (require.main === module) {
    const migration = new ConfigMigration();
    
    migration.migrate()
        .then(() => {
            console.log(chalk.green('🎉 Migration completed successfully!'));
            process.exit(0);
        })
        .catch((error) => {
            console.error(chalk.red('💥 Migration failed:'), error);
            process.exit(1);
        });
}
